/* eslint-disable */
/**
 * LLM服务模块 - 处理与大型语言模型的API交互
 */

import { ILLUSTRATION_GROUPING_PROMPT } from './promptTemplates';

// 从本地存储加载API设置
async function loadApiSettings() {
  try {
    console.log("开始加载LLM API设置...");
    let settings;

    // 1. 优先从服务器API获取设置
    try {
      console.log("从API获取用户设置...");
      const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.settings) {
          settings = result.settings;
          console.log("从API获取设置成功，设置内容:", {
            provider: settings.llm?.provider || '未设置',
            model: settings.llm?.defaultModel || '未设置'
          });
          // 将API获取的设置保存到localStorage中
          localStorage.setItem('usersettings', JSON.stringify(settings));
          return settings.llm;
        } else {
          console.warn("API返回的设置数据无效:", result);
        }
      } else {
        console.warn("API请求失败, 状态码:", response.status);
      }
    } catch (err) {
      console.error("从API获取设置时出错:", err);
    }

    // 2. 从localStorage读取设置
    console.log("尝试从localStorage读取设置...");
    const settingsStr = localStorage.getItem('usersettings');
    if (settingsStr) {
      try {
        settings = JSON.parse(settingsStr);
        console.log("从localStorage获取设置成功:", {
          provider: settings.llm?.provider || '未设置',
          model: settings.llm?.defaultModel || '未设置'
        });
        return settings.llm;
      } catch (err) {
        console.error("解析localStorage中的设置出错:", err);
      }
    } else {
      console.log("localStorage中没有设置数据");
    }

    // 3. 所有尝试都失败，返回默认设置
    console.warn("所有获取设置的尝试都失败，使用默认设置");
    const defaultSettings = {
      provider: 'openrouter',
      defaultModel: 'anthropic/claude-3-sonnet',
      temperature: 0.7,
      localUrl: 'http://localhost:8080'
    };
    return defaultSettings;
  } catch (error) {
    console.error("加载API设置时发生错误:", error);
    return {
      provider: 'openrouter',
      defaultModel: 'anthropic/claude-3-sonnet',
      temperature: 0.7,
      localUrl: 'http://localhost:8080'
    };
  }
}

// 根据提供商获取API密钥
function getApiKeyForProvider(llmSettings) {
  // 检查llmSettings是否有效
  if (!llmSettings) {
    console.error("提供的LLM设置无效");
    return '';
  }

  // 根据提供商返回对应的API密钥
  let apiKey = '';
  switch(llmSettings.provider) {
    case 'openrouter':
      apiKey = llmSettings.openrouterApiKey;
      break;
    case 'google':
      apiKey = llmSettings.googleApiKey;
      break;
    case 'openai':
      apiKey = llmSettings.openaiApiKey;
      break;
    case 'anthropic':
      apiKey = llmSettings.anthropicApiKey;
      break;
    case 'local':
      apiKey = llmSettings.localApiKey;
      break;
    default:
      console.warn(`未知的提供商: ${llmSettings.provider}`);
      apiKey = '';
  }

  // 添加调试信息
  if (!apiKey) {
    console.warn(`未找到${llmSettings.provider}提供商的API密钥`);
  } else {
    console.log(`成功获取${llmSettings.provider}提供商的API密钥`);
  }

  return apiKey;
}

/**
 * 调用LLM API进行智能分段（支持分批处理）
 * @param {Array} subtitles - 字幕数组
 * @param {Object} options - 选项
 * @returns {Promise<Object>} - 分组结果和原始响应
 */
export async function performSemanticGrouping(subtitles, options = {}) {
  console.log('开始智能分段分析...');
  console.log('接收到的 subtitles 数组长度:', subtitles.length);
  if (subtitles.length > 0) {
    console.log('subtitles 数组前3项示例:');
    for (let i = 0; i < Math.min(subtitles.length, 3); i++) {
      console.log(`  Item ${i + 1}:`, JSON.stringify(subtitles[i], null, 2));
    }
  }

  // 如果字幕数量超过50条，使用分批处理
  const BATCH_SIZE = 50;
  if (subtitles.length > BATCH_SIZE) {
    console.log(`字幕数量(${subtitles.length})超过${BATCH_SIZE}条，启用分批处理模式`);
    return await performBatchSemanticGrouping(subtitles, options);
  }

  // 加载设置 - 如果提供了userSettings，则优先使用
  let settings;
  if (options.userSettings) {
    // 用户传入的设置
    console.log('使用传入的用户LLM设置');
    const userSettings = options.userSettings;

    // 详细记录传入的设置
    console.log('传入的用户设置详情:', {
      provider: userSettings.provider,
      defaultModel: userSettings.defaultModel,
      temperature: userSettings.temperature
    });

    const apiKey = getApiKeyForProvider(userSettings);
    console.log(`用户设置的提供商 ${userSettings.provider} 的API密钥状态: ${apiKey ? '有效' : '无效'}`);

    if (apiKey) {
      settings = {
        provider: userSettings.provider || 'openrouter',
        apiKey: apiKey,
        model: userSettings.defaultModel, // 使用用户设置的模型
        temperature: userSettings.temperature || 0.7,
        maxOutputTokens: userSettings.maxOutputTokens
      };
      console.log('成功使用用户配置的LLM设置');
    } else {
      console.warn('传入的用户设置中没有有效的API密钥，将使用默认设置');
      settings = await loadApiSettings();
      // 转换loadApiSettings返回的格式为内部使用格式
      if (settings) {
        const tempApiKey = getApiKeyForProvider(settings);
        settings = {
          provider: settings.provider || 'openrouter',
          apiKey: tempApiKey,
          model: settings.defaultModel, // 确保使用defaultModel而不是model
          temperature: settings.temperature || 0.7,
          maxOutputTokens: settings.maxOutputTokens
        };
      }
    }
  } else {
    // 没有传入设置，使用默认加载方式
    console.log('未传入用户设置，使用默认加载方式');
    const loadedSettings = await loadApiSettings();
    // 转换loadApiSettings返回的格式为内部使用格式
    if (loadedSettings) {
      const tempApiKey = getApiKeyForProvider(loadedSettings);
      settings = {
        provider: loadedSettings.provider || 'openrouter',
        apiKey: tempApiKey,
        model: loadedSettings.defaultModel, // 确保使用defaultModel而不是model
        temperature: loadedSettings.temperature || 0.7,
        maxOutputTokens: loadedSettings.maxOutputTokens
      };
    } else {
      // 如果loadedSettings为null，使用一个默认值
      settings = {
        provider: 'openrouter',
        apiKey: '',
        model: 'anthropic/claude-3-sonnet',
        temperature: 0.7,
        maxOutputTokens: 4096
      };
    }
  }

  console.log("最终使用的设置信息:", {
    provider: settings.provider,
    model: settings.model,
    hasApiKey: !!settings.apiKey,
    temperature: settings.temperature
  });

  // 如果没有API密钥，且不是强制使用API，则直接报错
  if (!settings.apiKey && !options.forceUseApi) {
    throw new Error('未找到API密钥，无法使用AI智能分组功能。');
  }
  if (!settings.apiKey && options.forceUseApi) {
    throw new Error('未配置API密钥，无法使用AI功能。请在设置中配置API密钥。');
  }

  console.log(`使用LLM提供商: ${settings.provider}, 模型: ${settings.model}`);

  // 准备输入文本（合并所有字幕内容）
  const textContent = subtitles.map(item => item.content).join('\n');
  console.log(`已准备${subtitles.length}条字幕内容，总字符数: ${textContent.length}`);
  console.log('生成的 textContent 前500字符:', textContent.substring(0, 500));

  // 准备提示词 - 确保options.maxGroupSize被正确传递和解析
  const maxGroupSizeForPrompt = options.maxGroupSize ? parseInt(options.maxGroupSize, 10) : 0;
  const prompt = generatePrompt(textContent, { maxGroupSize: maxGroupSizeForPrompt });

  try {
    // 根据不同提供商调用不同的API
    let apiResult;

    switch(settings.provider) {
      case 'openrouter':
        apiResult = await callOpenRouterApi(prompt, settings, subtitles);
        break;
      case 'google':
        apiResult = await callGoogleApi(prompt, settings, subtitles);
        break;
      case 'openai':
        apiResult = await callOpenAIApi(prompt, settings, subtitles);
        break;
      default:
        throw new Error(`不支持的提供商: ${settings.provider}`);
    }

    if (apiResult && apiResult.rawTextResponse) {
      const result = parseGroupingByContentLines(apiResult.rawTextResponse);
      return {
        groupingResult: result.groupingResult,
        rawText: result.rawText,
        originalResponse: apiResult.originalResponse
      };
    } else {
      throw new Error('AI分组API调用失败或未返回有效响应。');
    }
  } catch (error) {
    const enhancedError = new Error(`AI分析出错: ${error.message}`);
    enhancedError.originalResponse = error.originalResponse || null;
    throw enhancedError;
  }
}

/**
 * 分批处理智能分段
 * @param {Array} subtitles - 字幕数组
 * @param {Object} options - 选项
 * @returns {Promise<Object>} - 分组结果和原始响应
 */
async function performBatchSemanticGrouping(subtitles, options = {}) {
  const BATCH_SIZE = 50;
  const batches = [];

  // 将字幕分割成批次
  for (let i = 0; i < subtitles.length; i += BATCH_SIZE) {
    batches.push(subtitles.slice(i, i + BATCH_SIZE));
  }

  console.log(`分批处理：总共${subtitles.length}条字幕，分为${batches.length}个批次`);

  const allGroupingResults = [];
  const allRawTexts = [];
  const allOriginalResponses = [];

  // 处理每个批次
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    const batchStartIndex = batchIndex * BATCH_SIZE;

    console.log(`处理第${batchIndex + 1}/${batches.length}批次，包含${batch.length}条字幕 (索引${batchStartIndex}-${batchStartIndex + batch.length - 1})`);

    // 更新进度回调
    if (options.onProgress) {
      options.onProgress({
        currentBatch: batchIndex + 1,
        totalBatches: batches.length,
        currentBatchSize: batch.length,
        processedCount: batchStartIndex,
        totalCount: subtitles.length
      });
    }

    try {
      // 处理单个批次
      const batchResult = await processSingleBatch(batch, { ...options, batchIndex, totalBatches: batches.length });

      if (batchResult && batchResult.groupingResult) {
        allGroupingResults.push(...batchResult.groupingResult);
        if (batchResult.rawText) {
          allRawTexts.push(batchResult.rawText);
        }
        if (batchResult.originalResponse) {
          allOriginalResponses.push(batchResult.originalResponse);
        }
      }

      // 批次间短暂延迟，避免API限制
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }

    } catch (error) {
      console.error(`第${batchIndex + 1}批次处理失败:`, error);
      throw new Error(`分批处理失败：第${batchIndex + 1}批次出错 - ${error.message}`);
    }
  }

  // 合并所有批次的结果
  const mergedRawText = allRawTexts.length > 0 ? allRawTexts.join('\n\n') : '';
  const mergedOriginalResponse = allOriginalResponses.length > 0 ? allOriginalResponses.join('\n\n--- 批次分隔 ---\n\n') : '';

  console.log(`分批处理完成：共处理${batches.length}个批次，生成${allGroupingResults.length}个分组`);

  return {
    groupingResult: allGroupingResults,
    rawText: mergedRawText,
    originalResponse: mergedOriginalResponse,
    batchInfo: {
      totalBatches: batches.length,
      totalSubtitles: subtitles.length,
      batchSize: BATCH_SIZE
    }
  };
}

/**
 * 处理单个批次
 * @param {Array} batchSubtitles - 批次字幕数组
 * @param {Object} options - 选项
 * @returns {Promise<Object>} - 批次分组结果
 */
async function processSingleBatch(batchSubtitles, options = {}) {
  // 加载设置 - 如果提供了userSettings，则优先使用
  let settings;
  if (options.userSettings) {
    console.log('使用前端传递的用户LLM设置');
    settings = options.userSettings;
  } else {
    console.log('从本地加载LLM设置');
    settings = await loadLLMSettings();
  }

  if (!settings || settings.provider === 'none') {
    throw new Error('无法加载LLM设置或未配置LLM提供商');
  }

  // 准备文本内容
  const textContent = batchSubtitles.map(item => item.content).join('\n');
  const prompt = generatePrompt(textContent, options);

  console.log(`批次处理 - 提供商: ${settings.provider}, 模型: ${settings.defaultModel || settings.model || 'default'}`);
  console.log(`批次处理 - 文本长度: ${textContent.length} 字符, 字幕数量: ${batchSubtitles.length}`);

  try {
    // 根据不同提供商调用不同的API
    let apiResult;

    console.log(`批次处理 - 调用${settings.provider} API...`);

    switch(settings.provider) {
      case 'openrouter':
        apiResult = await callOpenRouterApi(prompt, settings, batchSubtitles);
        break;
      case 'google':
        apiResult = await callGoogleApi(prompt, settings, batchSubtitles);
        break;
      case 'openai':
        apiResult = await callOpenAIApi(prompt, settings, batchSubtitles);
        break;
      default:
        throw new Error(`不支持的提供商: ${settings.provider}`);
    }

    console.log(`批次处理 - ${settings.provider} API调用完成，结果:`, apiResult);

    if (apiResult && apiResult.rawTextResponse) {
      const result = parseGroupingByContentLines(apiResult.rawTextResponse);
      return {
        groupingResult: result.groupingResult,
        rawText: result.rawText,
        originalResponse: apiResult.originalResponse
      };
    } else {
      console.error('批次API调用失败，apiResult:', apiResult);
      throw new Error('批次AI分组API调用失败或未返回有效响应。');
    }
  } catch (error) {
    const enhancedError = new Error(`批次AI分析出错: ${error.message}`);
    enhancedError.originalResponse = error.originalResponse || null;
    throw enhancedError;
  }
}

/**
 * 使用自定义提示词调用LLM API（支持分批处理）
 * @param {Array} subtitles - 字幕数组
 * @param {String} customPrompt - 自定义提示词
 * @param {Object} options - 选项 (可选)
 * @returns {Promise<Object>} - 分组结果和原始响应
 */
export async function performCustomPrompting(subtitles, customPrompt, options = {}) {
  console.log('开始处理自定义提示词...');
  console.log('接收到的选项:', options);

  // 如果字幕数量超过50条，使用分批处理
  const BATCH_SIZE = 50;
  if (subtitles.length > BATCH_SIZE) {
    console.log(`字幕数量(${subtitles.length})超过${BATCH_SIZE}条，启用自定义提示词分批处理模式`);
    return await performBatchCustomPrompting(subtitles, customPrompt, options);
  }

  // 加载设置 - 如果提供了userSettings，则优先使用
  let settings;
  if (options.userSettings) {
    // 用户传入的设置
    console.log('使用传入的用户LLM设置');
    const userSettings = options.userSettings;

    // 详细记录传入的设置
    console.log('传入的用户设置详情:', {
      provider: userSettings.provider,
      defaultModel: userSettings.defaultModel,
      temperature: userSettings.temperature
    });

    const apiKey = getApiKeyForProvider(userSettings);
    console.log(`用户设置的提供商 ${userSettings.provider} 的API密钥状态: ${apiKey ? '有效' : '无效'}`);

    if (apiKey) {
      settings = {
        provider: userSettings.provider || 'openrouter',
        apiKey: apiKey,
        model: userSettings.defaultModel, // 使用用户设置的模型
        temperature: userSettings.temperature || 0.7,
        maxOutputTokens: userSettings.maxOutputTokens
      };
      console.log('成功使用用户配置的LLM设置');
    } else {
      console.warn('传入的用户设置中没有有效的API密钥，将使用默认设置');
      settings = await loadApiSettings();
      // 转换loadApiSettings返回的格式为内部使用格式
      if (settings) {
        const tempApiKey = getApiKeyForProvider(settings);
        settings = {
          provider: settings.provider || 'openrouter',
          apiKey: tempApiKey,
          model: settings.defaultModel, // 确保使用defaultModel而不是model
          temperature: settings.temperature || 0.7,
          maxOutputTokens: settings.maxOutputTokens
        };
      }
    }
  } else {
    // 没有传入设置，使用默认加载方式
    console.log('未传入用户设置，使用默认加载方式');
    const loadedSettings = await loadApiSettings();
    // 转换loadApiSettings返回的格式为内部使用格式
    if (loadedSettings) {
      const tempApiKey = getApiKeyForProvider(loadedSettings);
      settings = {
        provider: loadedSettings.provider || 'openrouter',
        apiKey: tempApiKey,
        model: loadedSettings.defaultModel, // 确保使用defaultModel而不是model
        temperature: loadedSettings.temperature || 0.7,
        maxOutputTokens: loadedSettings.maxOutputTokens
      };
    } else {
      // 如果loadedSettings为null，使用一个默认值
      settings = {
        provider: 'openrouter',
        apiKey: '',
        model: 'anthropic/claude-3-sonnet',
        temperature: 0.7,
        maxOutputTokens: 4096
      };
    }
  }

  console.log("最终使用的设置信息:", {
    provider: settings.provider,
    model: settings.model,
    hasApiKey: !!settings.apiKey,
    temperature: settings.temperature
  });

  // 检查API密钥
  if (!settings.apiKey) {
    throw new Error('未配置API密钥，无法使用自定义提示词功能。请在设置中配置API密钥。');
  }

  console.log(`使用LLM提供商: ${settings.provider}, 模型: ${settings.model}`);
  console.log(`自定义提示词长度: ${customPrompt.length}字符`);

  try {
    // 根据不同提供商调用不同的API
    let apiResult;

    switch(settings.provider) {
      case 'openrouter':
        apiResult = await callOpenRouterApi(customPrompt, settings, subtitles);
        break;
      case 'google':
        apiResult = await callGoogleApi(customPrompt, settings, subtitles);
        break;
      case 'openai':
        apiResult = await callOpenAIApi(customPrompt, settings, subtitles);
        break;
      default:
        throw new Error(`不支持的提供商: ${settings.provider}`);
    }

    if (apiResult && apiResult.rawTextResponse) {
      const result = parseGroupingByContentLines(apiResult.rawTextResponse);
      return {
        groupingResult: result.groupingResult,
        rawText: result.rawText,
        originalResponse: apiResult.originalResponse
      };
    } else {
      throw new Error(`处理自定义提示词时，提供商 ${settings.provider} 未返回有效响应。`);
    }
  } catch (error) {
    const enhancedError = new Error(`处理自定义提示词出错: ${error.message}`);
    enhancedError.originalResponse = error.originalResponse || null;
    throw enhancedError;
  }
}

/**
 * 分批处理自定义提示词
 * @param {Array} subtitles - 字幕数组
 * @param {String} customPrompt - 自定义提示词
 * @param {Object} options - 选项
 * @returns {Promise<Object>} - 分组结果和原始响应
 */
async function performBatchCustomPrompting(subtitles, customPrompt, options = {}) {
  const BATCH_SIZE = 50;
  const batches = [];

  // 将字幕分割成批次
  for (let i = 0; i < subtitles.length; i += BATCH_SIZE) {
    batches.push(subtitles.slice(i, i + BATCH_SIZE));
  }

  console.log(`自定义提示词分批处理：总共${subtitles.length}条字幕，分为${batches.length}个批次`);

  const allGroupingResults = [];
  const allRawTexts = [];
  const allOriginalResponses = [];

  // 处理每个批次
  for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
    const batch = batches[batchIndex];
    const batchStartIndex = batchIndex * BATCH_SIZE;

    console.log(`处理第${batchIndex + 1}/${batches.length}批次，包含${batch.length}条字幕 (索引${batchStartIndex}-${batchStartIndex + batch.length - 1})`);

    // 更新进度回调
    if (options.onProgress) {
      options.onProgress({
        currentBatch: batchIndex + 1,
        totalBatches: batches.length,
        currentBatchSize: batch.length,
        processedCount: batchStartIndex,
        totalCount: subtitles.length
      });
    }

    try {
      // 处理单个批次
      const batchResult = await processSingleBatchWithCustomPrompt(batch, customPrompt, { ...options, batchIndex, totalBatches: batches.length });

      if (batchResult && batchResult.groupingResult) {
        allGroupingResults.push(...batchResult.groupingResult);
        if (batchResult.rawText) {
          allRawTexts.push(batchResult.rawText);
        }
        if (batchResult.originalResponse) {
          allOriginalResponses.push(batchResult.originalResponse);
        }
      }

      // 批次间短暂延迟，避免API限制
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }

    } catch (error) {
      console.error(`第${batchIndex + 1}批次处理失败:`, error);
      throw new Error(`自定义提示词分批处理失败：第${batchIndex + 1}批次出错 - ${error.message}`);
    }
  }

  // 合并所有批次的结果
  const mergedRawText = allRawTexts.length > 0 ? allRawTexts.join('\n\n') : '';
  const mergedOriginalResponse = allOriginalResponses.length > 0 ? allOriginalResponses.join('\n\n--- 批次分隔 ---\n\n') : '';

  console.log(`自定义提示词分批处理完成：共处理${batches.length}个批次，生成${allGroupingResults.length}个分组`);

  return {
    groupingResult: allGroupingResults,
    rawText: mergedRawText,
    originalResponse: mergedOriginalResponse,
    batchInfo: {
      totalBatches: batches.length,
      totalSubtitles: subtitles.length,
      batchSize: BATCH_SIZE
    }
  };
}

/**
 * 处理单个批次（自定义提示词）
 * @param {Array} batchSubtitles - 批次字幕数组
 * @param {String} customPrompt - 自定义提示词
 * @param {Object} options - 选项
 * @returns {Promise<Object>} - 批次分组结果
 */
async function processSingleBatchWithCustomPrompt(batchSubtitles, customPrompt, options = {}) {
  // 加载设置 - 如果提供了userSettings，则优先使用
  let settings;
  if (options.userSettings) {
    console.log('使用前端传递的用户LLM设置');
    settings = options.userSettings;
  } else {
    console.log('从本地加载LLM设置');
    settings = await loadLLMSettings();
  }

  if (!settings || settings.provider === 'none') {
    throw new Error('无法加载LLM设置或未配置LLM提供商');
  }

  // 准备文本内容和自定义提示词
  const textContent = batchSubtitles.map(item => item.content).join('\n');
  const numberOfLines = batchSubtitles.length;

  // 替换自定义提示词中的变量
  let finalPrompt = customPrompt
    .replace(/\{numberOfLines\}/g, numberOfLines)
    .replace(/\{textContent\}/g, textContent);

  console.log(`批次自定义提示词处理 - 提供商: ${settings.provider}, 模型: ${settings.defaultModel || settings.model || 'default'}`);
  console.log(`批次自定义提示词处理 - 文本长度: ${textContent.length} 字符, 字幕数量: ${batchSubtitles.length}`);

  try {
    // 根据不同提供商调用不同的API
    let apiResult;

    switch(settings.provider) {
      case 'openrouter':
        apiResult = await callOpenRouterApi(finalPrompt, settings, batchSubtitles);
        break;
      case 'google':
        apiResult = await callGoogleApi(finalPrompt, settings, batchSubtitles);
        break;
      case 'openai':
        apiResult = await callOpenAIApi(finalPrompt, settings, batchSubtitles);
        break;
      default:
        throw new Error(`不支持的提供商: ${settings.provider}`);
    }

    if (apiResult && apiResult.rawTextResponse) {
      const result = parseGroupingByContentLines(apiResult.rawTextResponse);
      return {
        groupingResult: result.groupingResult,
        rawText: result.rawText,
        originalResponse: apiResult.originalResponse
      };
    } else {
      throw new Error('批次自定义提示词API调用失败或未返回有效响应。');
    }
  } catch (error) {
    const enhancedError = new Error(`批次自定义提示词分析出错: ${error.message}`);
    enhancedError.originalResponse = error.originalResponse || null;
    throw enhancedError;
  }
}

/**
 * 生成智能分段提示词
 */
function generatePrompt(textContent, options = {}) {
  const numberOfLines = textContent.split('\n').length;
  return ILLUSTRATION_GROUPING_PROMPT
    .replace(/\{numberOfLines\}/g, numberOfLines)
    .replace(/\{textContent\}/g, textContent);
}

/**
 * 调用OpenRouter API
 */
async function callOpenRouterApi(prompt, settings, subtitles) {
  const endpoint = 'https://openrouter.ai/api/v1/chat/completions';
  const requestData = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${settings.apiKey}`,
      'HTTP-Referer': location.origin,
      'X-Title': 'Voice Comic Generator',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: settings.model,
      messages: [{ role: 'user', content: prompt }],
      temperature: settings.temperature || 0.7,
      max_tokens: settings.maxOutputTokens || 4096,
      reasoning: { effort: "high", exclude: true }
    })
  };

  const response = await fetch(endpoint, requestData);
  const data = await response.json();

  if (!response.ok || !data.choices || !data.choices[0]) {
    const error = new Error('API请求失败: ' + (data.error?.message || response.statusText));
    error.originalResponse = JSON.stringify(data, null, 2);
    throw error;
  }

  try {
    // 保存原始响应
    const originalResponse = JSON.stringify(data, null, 2);
    const content = data.choices[0].message.content.trim();

    // 直接返回原始文本内容和原始响应
    return {
        rawTextResponse: content,
        originalResponse
    };
  } catch (error) {
    console.error('解析OpenRouter API响应出错:', error);
    error.originalResponse = JSON.stringify(data, null, 2);
    throw error;
  }
}

/**
 * 调用Google API
 */
async function callGoogleApi(prompt, settings, subtitles) {
  const API_KEY = settings.apiKey;

  // 确保模型名称存在并且是字符串
  const model = settings.model || settings.defaultModel || 'gemini-pro';
  if (!model || typeof model !== 'string') {
    throw new Error(`无效的模型名称: ${model}`);
  }

  // 确保模型名称的正确性，有些模型名称可能不需要 'models/' 前缀
  const modelName = model.startsWith('models/') ? model : `models/${model}`;

  // 打印详细的模型信息用于调试
  console.log(`Google API调用 - 使用的模型设置:`, {
    原始模型名: settings.model,
    默认模型名: settings.defaultModel,
    最终使用模型: model,
    处理后模型名: modelName,
    提供商: settings.provider
  });

  // 优先用 settings.maxOutputTokens，没有则用 4096
  const maxOutputTokens = settings.maxOutputTokens || 4096;

  const API_URL = `https://generativelanguage.googleapis.com/v1beta/${modelName}:generateContent?key=${API_KEY}`;

  const requestBody = {
    contents: [
      {
        parts: [
          { text: prompt },
        ],
      },
    ],
    generationConfig: {
      temperature: settings.temperature || 0.7,
      maxOutputTokens: maxOutputTokens,
    },
  };

  console.log("Google API请求URL:", API_URL);
  console.log("Google API请求体:", JSON.stringify(requestBody, null, 2));

  const response = await fetch(API_URL, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestBody)
  });
  const data = await response.json();

  if (!response.ok || !data.candidates || !data.candidates[0]) {
    const error = new Error('API请求失败: ' + (data.error?.message || response.statusText));
    error.originalResponse = JSON.stringify(data, null, 2);
    throw error;
  }

  try {
    // 保存原始响应
    const originalResponse = JSON.stringify(data, null, 2);

    // 从不同响应格式中提取文本
    let content = '';
    if (data.candidates[0].content && data.candidates[0].content.parts && data.candidates[0].content.parts.length > 0) {
      content = data.candidates[0].content.parts[0].text;
    } else if (data.candidates[0].text) {
      content = data.candidates[0].text;
    }

    // 直接返回原始文本内容和原始响应
    return {
      rawTextResponse: content.trim(),
      originalResponse
    };
  } catch (error) {
    console.error('解析Google API响应出错:', error);
    const enhancedError = new Error(error.message);
    enhancedError.originalResponse = JSON.stringify(data, null, 2);
    throw enhancedError;
  }
}

/**
 * 调用OpenAI API
 */
async function callOpenAIApi(prompt, settings, subtitles) {
  const endpoint = 'https://api.openai.com/v1/chat/completions';
  const requestData = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${settings.apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: settings.model,
      messages: [{ role: 'user', content: prompt }],
      temperature: settings.temperature || 0.7,
      max_tokens: 4096,
    })
  };

  const response = await fetch(endpoint, requestData);
  const data = await response.json();

  if (!response.ok || !data.choices || !data.choices[0]) {
    const error = new Error('API请求失败: ' + (data.error?.message || response.statusText));
    error.originalResponse = JSON.stringify(data, null, 2);
    throw error;
  }

  try {
    // 保存原始响应
    const originalResponse = JSON.stringify(data, null, 2);
    const content = data.choices[0].message.content.trim();

    // 直接返回原始文本内容和原始响应
    return {
        rawTextResponse: content,
        originalResponse
    };
  } catch (error) {
    console.error('解析OpenAI API响应出错:', error);
    const enhancedError = new Error(error.message);
    enhancedError.originalResponse = JSON.stringify(data, null, 2);
    throw enhancedError;
  }
}

/**
 * 解析AI返回的分组文本（每行'1. xxx, yyy, zzz'）为二维数组
 * @param {string} rawText - AI返回的原始分组文本
 * @returns {Object} - 包含分组后的字幕内容数组和原始文本
 */
function parseGroupingByContentLines(rawText) {
  if (!rawText || typeof rawText !== 'string') {
    console.warn('parseGroupingByContentLines: rawText 为空或不是字符串:', rawText);
    return {
      groupingResult: [],
      rawText: rawText || ''
    };
  }

  // 保存原始文本，以便在需要时可以直接使用原始格式
  const originalText = rawText;

  const groupRegex = /^(\d+)\.\s*(.+)$/gm;
  const groups = [];
  let match;
  while ((match = groupRegex.exec(rawText)) !== null) {
    const groupContent = match[2].trim();
    // 按逗号分割
    const sentences = groupContent.split(',').map(s => s.trim()).filter(Boolean);
    if (sentences.length > 0) groups.push(sentences);
  }

  // 返回分组结果和原始文本
  return {
    groupingResult: groups,
    rawText: originalText
  };
}

export default {
  performSemanticGrouping,
  performCustomPrompting
};