<template>
  <BaseDrawer
    :show="showProp"
    @update:show="val => emit('update:show', val)"
    :drawer-width="'70vw'"
  >
    <div class="reasoning-drawer-content">
      <div class="drawer-header">
        <h3>全局推理 ({{ effectiveProjectTitle }} <span v-if="effectiveChapterTitle">/ {{ effectiveChapterTitle }}</span>)</h3>
        <div class="header-actions">
          <button
            @click="openTextViewerModal"
            class="icon-button"
            title="查看源文本"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              width="20"
              height="20"
            >
              <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
            </svg>
            <span style="margin-left: 5px;">源文本</span>
          </button>
          <button
            @click="openPromptEditor"
            class="icon-button"
            title="编辑AI指令 (Prompt)"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="currentColor"
              width="20"
              height="20"
            ><path d="M12.962 2.19l7.846 7.846a2.25 2.25 0 010 3.182l-1.407 1.407-9.253-9.253 1.407-1.407a2.25 2.25 0 011.407-.675zm-1.407 1.407L3.948 11.204a2.252 2.252 0 00-.675 1.407V18.75c0 .414.336.75.75.75h6.139a2.252 2.252 0 001.407-.675L19.176 10.22l-9.028-9.028-1.591 1.591zM4.5 19.5V13.06l5.432 5.432H4.5zM12 19.5H6.568l5.432-5.432L12 14.068V19.5z" /></svg>
          </button>
          <button
            @click="closeDrawer"
            class="close-drawer-button"
            title="关闭"
          >
            &times;
          </button>
        </div>
      </div>

      <div class="drawer-body">
        <div class="top-action-buttons-container">
          <button
            @click="analyze"
            :disabled="isLoading || !inputText.trim()"
            class="analyze-button small-action-btn"
          >
            <span v-if="isLoading">
              <svg
                class="spinner"
                viewBox="0 0 50 50"
              ><circle
                class="path"
                cx="25"
                cy="25"
                r="20"
                fill="none"
                stroke-width="5"
              /></svg>
              分析中...
            </span>
            <span v-else>
              <i class="ri-magic-line" /> 智能角色
            </span>
          </button>
          <button
            @click="handleSmartNaming"
            :disabled="isSmartNamingLoading || !inputText.trim()"
            class="smart-naming-btn small-action-btn"
          >
            <span v-if="isSmartNamingLoading">
              <svg
                class="spinner"
                viewBox="0 0 50 50"
              ><circle
                class="path"
                cx="25"
                cy="25"
                r="20"
                fill="none"
                stroke-width="5"
              /></svg>
              分析中...
            </span>
            <span v-else>
              <i class="ri-lightbulb-flash-line" /> 智能特名
            </span>
          </button>
          <button
            @click="handleSmartScene"
            :disabled="isSmartSceneLoading || !inputText.trim()"
            class="smart-naming-btn small-action-btn"
          >
            <span v-if="isSmartSceneLoading">
              <svg
                class="spinner"
                viewBox="0 0 50 50"
              ><circle
                class="path"
                cx="25"
                cy="25"
                r="20"
                fill="none"
                stroke-width="5"
              /></svg>
              分析中...
            </span>
            <span v-else>
              <i class="ri-landscape-line" /> 智能场景
            </span>
          </button>
          <input
            type="file"
            @change="handlePresetFileSelected_drawer"
            accept=".txt"
            ref="presetFileInput_drawer"
            style="display: none;"
          >
          <button
            @click="triggerPresetFileInput_drawer"
            :disabled="presetImportLoading_drawer"
            class="import-preset-button small-action-btn"
          >
            <i class="ri-folder-open-line" /> 导入预设 (.txt)
          </button>
        </div>
        <p
          v-if="presetImportMessage_drawer"
          :class="`message message-${presetImportMessageType_drawer} drawer-message`"
        >
          {{ presetImportMessage_drawer }}
        </p>

        <div class="tab-nav-bar">
          <button
            class="tab-nav-btn"
            :class="{ active: activeTab === TABS.ANALYSIS }"
            @click="activeTab = TABS.ANALYSIS"
          >
            <i class="ri-search-eye-line"></i>
            当前角色
          </button>
          <button
            class="tab-nav-btn"
            :class="{ active: activeTab === TABS.SPECIAL }"
            @click="activeTab = TABS.SPECIAL"
          >
            <i class="ri-star-line"></i>
            当前特名
          </button>
          <button
            class="tab-nav-btn"
            :class="{ active: activeTab === TABS.SCENE }"
            @click="activeTab = TABS.SCENE"
          >
            <i class="ri-landscape-line"></i>
            当前场景
          </button>
          <button
            class="tab-nav-btn"
            :class="{ active: activeTab === TABS.PRESET }"
            @click="activeTab = TABS.PRESET"
          >
            <i class="ri-user-2-line"></i>
            预设角色
          </button>
        </div>
        <div class="tab-nav-divider"></div>
        <div class="tab-content">
          <div v-if="activeTab === TABS.ANALYSIS" class="panel analysis-panel">
            <h4 class="panel-title">
              当前角色 <span v-if="allResultItemsCount > 0">({{ allResultItemsCount }})</span>
            </h4>
            <CurrentAnalysisResultsPanel
              :analyzed-items="currentAnalyzedItems"
              :raw-output="rawLlmOutputText"
              :is-loading="isLoading"
              :error-msg="error ? (error.message || error) : ''"
              @request-add-to-chapter="handleRequestAddToChapter"
              :can-add-to-chapter="false"
              @edit-character="handleEditCurrentCharacter"
              @delete-character="handleDeleteCurrentCharacter"
            />
          </div>
          <div v-if="activeTab === TABS.PRESET" class="panel preset-panel">
            <h4 class="panel-title preset-title">
              预设角色 ({{ displayedPresetItems.length }})
            </h4>
            <PresetManagerPanel
              :project-title="projectTitle"
              ref="presetManagerPanelRef"
              :items="displayedPresetItems"
              :is-loading="globalPresetsLoading"
              :error="globalPresetsError ? globalPresetsError.toString() : null"
              @delete-item="handleDeletePresetItem"
              @edit-item="handleEditPresetItem"
            />
          </div>
          <div v-if="activeTab === TABS.SPECIAL" class="panel special-panel">
            <h4 class="panel-title">当前特名 ({{ currentAnalyzedItems.specialItems.length }})</h4>
            <CurrentSpecialPanel
              :items="currentAnalyzedItems.specialItems"
              :onEdit="handleEditSpecial"
              :onDelete="handleDeleteSpecial"
            />
          </div>
          <div v-if="activeTab === TABS.SCENE" class="panel scene-panel">
            <CurrentScenePanel :items="currentAnalyzedItems.scenes" />
          </div>
        </div>
      </div>

      <PromptEditorModal
        :show="showPromptEditorModal"
        :promptsMap="promptsMap"
        :defaultPromptsMap="defaultPromptsMap"
        :currentType="currentPromptType"
        :promptTypeOptions="promptTypeOptions"
        @update:show="showPromptEditorModal = $event"
        @save-prompt="handleSavePrompt"
        @update:type="handlePromptTypeChange"
      />
      <PresetCharacterEditor
        v-if="showCharacterEditor"
        :show="showCharacterEditor"
        :character="editingCharacter"
        @save="handleSaveCharacter"
        @close="handleCloseEditor"
      />
    </div>
  </BaseDrawer>

  <Teleport to="body">
    <TextViewerModal
      :show="showTextViewerModal"
      :text-to-show="inputText || '无文本内容可显示'"
      @close="showTextViewerModal = false"
    />
  </Teleport>
  <ClassifyUnmarkedEntriesModal
    :show="showClassifyModal"
    :lines-to-classify="linesForClassification"
    @update:show="handleClassifyModalUpdateShow"
    @classify-complete="handleGlobalClassificationComplete"
    @classify-cancel="handleGlobalClassificationCancel"
  />
  <AnalysisResultConflictResolver
    v-if="showConflictResolver"
    :old-items="conflictOldItems"
    :new-items="conflictNewItems"
    :type="conflictType"
    @resolve="handleConflictResolve"
    @cancel="handleConflictCancel"
  />
</template>

<script setup>
import { ref, computed, watch, provide, onMounted, reactive } from 'vue';
import { useLLMService } from '../composables/useLLMService.js';
import { usePresetManager } from '../composables/usePresetManager.js';
import { useProjectContext } from '../composables/useProjectContext.js';
import PromptEditorModal from './PromptEditorModal.vue';
import PresetManagerPanel from './PresetManagerPanel.vue';
import CurrentAnalysisResultsPanel from './CurrentAnalysisResultsPanel.vue';
import BaseDrawer from './BaseDrawer.vue';
import TextViewerModal from './TextViewerModal.vue';
import ClassifyUnmarkedEntriesModal from './ClassifyUnmarkedEntriesModal.vue';
import AnalysisResultConflictResolver from './AnalysisResultConflictResolver.vue';
import PresetCharacterEditor from './PresetCharacterEditor.vue';
import CurrentSpecialPanel from './CurrentSpecialPanel.vue';
import CurrentScenePanel from './CurrentScenePanel.vue';

const props = defineProps({
  show: Boolean,
  initialText: String,
  projectTitle: String,
  chapterTitle: String,
});
const emit = defineEmits(['update:show', 'close-drawer']);

const showProp = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const inputText = ref(props.initialText || '');

// 添加日志记录initialText的内容和长度
console.log('[GlobalReasoningDrawer] 接收到的initialText长度:', props.initialText?.length || 0);
if (props.initialText) {
  console.log('[GlobalReasoningDrawer] initialText预览:', props.initialText.substring(0, 100) + (props.initialText.length > 100 ? '...' : ''));
}

const showTextViewerModal = ref(false);

const {
  isLoading,
  error,
  sendTextToLLM,
  sendTextToLLMIndependent
} = useLLMService();

// 独立的loading状态
const isSmartNamingLoading = ref(false);
const isSmartSceneLoading = ref(false);

const presetManagerPanelRef = ref(null);
const presetFileInput_drawer = ref(null);
const presetImportLoading_drawer = ref(false);
const presetImportMessage_drawer = ref('');
const presetImportMessageType_drawer = ref('');

const projectTitleForPresetManager = computed(() => props.projectTitle);
const {
  loadGlobalPresets,
  parseTextToPresetItems,
  processImportedData,
  displayedPresetItems,
  showClassifyModal,
  linesForClassification,
  handleClassificationComplete: onClassificationCompleteInternal,
  handleClassificationCancel: onClassificationCancelInternal,
  handleClassifyModalUpdateShow,
  globalPresetsLoading,
  globalPresetsError,
  deletePresetItem: deletePresetItemInternal,
  listFolders,
  saveProjectFile,
  activeGlobalPresets,
  saveGlobalJson
} = usePresetManager(projectTitleForPresetManager);

const projectContext = useProjectContext();

// 添加调试信息
watch(() => projectContext.projectData, (newData) => {
  console.log('[GlobalReasoningDrawer] 项目数据变化:', newData);
  if (newData?.value?.data?.rows) {
    console.log('[GlobalReasoningDrawer] 行数据数量:', newData.value.data.rows.length);
  } else {
    console.log('[GlobalReasoningDrawer] 项目数据结构:', {
      hasValue: !!newData?.value,
      hasData: !!newData?.value?.data,
      hasRows: !!newData?.value?.data?.rows,
      dataKeys: newData?.value?.data ? Object.keys(newData.value.data) : 'no data'
    });
  }
}, { deep: true, immediate: true });

const showSuccessMessage_drawer = (message) => {
  presetImportMessage_drawer.value = message;
  presetImportMessageType_drawer.value = 'success';
  setTimeout(() => {
    if (presetImportMessage_drawer.value === message) {
        presetImportMessage_drawer.value = '';
        presetImportMessageType_drawer.value = '';
    }
  }, 4000);
};

const showErrorMessage_drawer = (message) => {
  presetImportMessage_drawer.value = message;
  presetImportMessageType_drawer.value = 'error';
   setTimeout(() => {
    if (presetImportMessage_drawer.value === message) {
        presetImportMessage_drawer.value = '';
        presetImportMessageType_drawer.value = '';
    }
  }, 5000);
};

const showInfoMessage_drawer = (message) => {
  presetImportMessage_drawer.value = message;
  presetImportMessageType_drawer.value = 'info';
   setTimeout(() => {
    if (presetImportMessage_drawer.value === message) {
        presetImportMessage_drawer.value = '';
        presetImportMessageType_drawer.value = '';
    }
  }, 3000);
};

const triggerPresetFileInput_drawer = () => {
  if (presetFileInput_drawer.value) {
    presetFileInput_drawer.value.click();
  }
};

const handlePresetFileSelected_drawer = async (event) => {
      const file = event.target.files[0];
      if (!file) return;

  if (!props.projectTitle) {
    showErrorMessage_drawer('错误: 缺少项目名称，无法导入预设。');
    if (presetFileInput_drawer.value) presetFileInput_drawer.value.value = '';
        return;
      }

  presetImportLoading_drawer.value = true;
  showInfoMessage_drawer('正在导入文件...');

  try {
    const fileContent = await file.text();
    const { parsed, unclassified } = parseTextToPresetItems(fileContent);
    const result = await processImportedData(parsed, unclassified);

    if (result.needsClassification) {
      showInfoMessage_drawer('部分条目需要手动分类。请在弹出的窗口中完成操作。');
    } else if (result.saved) {
      showSuccessMessage_drawer(result.message || '预设已成功导入并保存！');
      await loadGlobalPresets();
    } else {
      showInfoMessage_drawer(result.message || '文件中未找到有效的、可直接导入的预设条目。');
    }
            } catch (err) {
    console.error("Error processing preset file in drawer:", err);
    showErrorMessage_drawer(`导入预设失败: ${err.message}`);
            } finally {
    presetImportLoading_drawer.value = false;
    if (presetFileInput_drawer.value) presetFileInput_drawer.value.value = '';
  }
};

const rawLlmOutputText = ref(null);

const currentAnalyzedItems = ref({
  eraBackground: '',
  characters: [],
  specialItems: [],
  techniques: [],
  scenes: []
});

const FALLBACK_DEFAULT_USER_PROMPT = `请分析以下文本，并严格按照指定格式提供信息：

1.  **时代背景分析**:
    *   请判断文本描述的时代背景。例如：玄幻、修仙、古代、现代、末世等。

2.  **角色提取与描述**:
    *   提取所有出现的角色名称。
    *   对于每个角色，提供以下特征的英文描述：性别 (Gender)，数字年龄 (Age)，长短/发型/发色 (Hair: length/style/color)，服饰/颜色 (Attire: type/color)，身材 (Build)。
    *   如果文本中明确给出了外貌描述，请依据文本。
    *   如果文本中未提供明确的外貌描述，请结合分析出的时代背景合理推断并生成描述。
    *   描述必须是确定的，避免使用 "maybe", "perhaps" 等不确定词汇。

3.  **其他元素提取**:
    *   提取文本中出现的特殊物品 (Special Items)、功法 (Techniques)、重要场景 (Key Scenes)。
    *   为这些元素提供简洁的英文描述，描述需符合时代背景。
    *   请注意特定名词的翻译准确性，例如"丹药"不应简单翻译为 "pill"，应寻求更贴切的表达。

**输出格式**:
请严格按照以下格式输出，每个条目占一行。角色名可以是中文，所有描述部分必须是英文：

时代背景: [分析出的时代背景，中文]
人物: [角色名] - [Gender: 男性/女性/其他, Age: 数字, Hair: 描述, Attire: 描述, Build: 描述]
特殊物品: [物品名] - [英文描述]
功法: [功法名] - [英文描述]
场景: [场景名] - [英文描述]

---
待分析文本如下：
[TEXT_PLACEHOLDER]`;

const currentSystemPrompt = ref('');
const actualDefaultPromptForEditor = ref(FALLBACK_DEFAULT_USER_PROMPT);
const showPromptEditorModal = ref(false);
const editablePrompt = ref('');

const effectiveProjectTitle = computed(() => props.projectTitle || projectContext.projectTitle.value);
const effectiveChapterTitle = computed(() => props.chapterTitle || projectContext.chapterTitle.value);

const loadActivePrompt = async () => {
  let loadedUserPrompt = null;
  let loadedDefaultPrompt = null;
  let finalPromptToUse = '';
  let defaultPromptForEditorToUse = FALLBACK_DEFAULT_USER_PROMPT;

  try {
    const defaultRes = await fetch('/api/local/prompt/default');
    if (defaultRes.ok) {
      loadedDefaultPrompt = await defaultRes.text();
      defaultPromptForEditorToUse = loadedDefaultPrompt;
      console.log("[Drawer] Successfully loaded API default prompt.");
          } else {
      console.warn("[Drawer] Failed to load API default prompt, status:", defaultRes.status, "Using fallback default.");
    }
  } catch (e) {
    console.error("[Drawer] Error fetching API default prompt:", e, "Using fallback default.");
  }
  actualDefaultPromptForEditor.value = defaultPromptForEditorToUse;

  try {
    const userRes = await fetch('/api/local/prompt/user');
    if (userRes.ok) {
      loadedUserPrompt = await userRes.text();
      finalPromptToUse = loadedUserPrompt;
      console.log("[Drawer] Successfully loaded user prompt.");
    } else if (userRes.status === 404) {
      console.log("[Drawer] User prompt not found, will use the (API or fallback) default prompt.");
      finalPromptToUse = defaultPromptForEditorToUse;
          } else {
      console.warn("[Drawer] Failed to load user prompt, status:", userRes.status, "Using the (API or fallback) default prompt.");
      finalPromptToUse = defaultPromptForEditorToUse;
    }
  } catch (e) {
    console.error("[Drawer] Error fetching user prompt:", e, "Using the (API or fallback) default prompt.");
    finalPromptToUse = defaultPromptForEditorToUse;
  }

  currentSystemPrompt.value = finalPromptToUse;
  editablePrompt.value = finalPromptToUse;
  rawLlmOutputText.value = null;
  if (error.value) error.value = null;
  showTextViewerModal.value = false;
};

const validateCurrentChapter = async () => {
  if (!props.projectTitle || !props.chapterTitle) return;

  isValidatingChapter.value = true;

  try {
    const chapters = await listFolders(props.projectTitle);
    isChapterValid.value = chapters.includes(props.chapterTitle);

    if (!isChapterValid.value) {
      console.warn(`[GlobalReasoningDrawer] 警告：章节 "${props.chapterTitle}" 在项目 "${props.projectTitle}" 中不存在`);
    }
  } catch (err) {
    console.error('[GlobalReasoningDrawer] 验证章节出错:', err);
    // 验证失败时假定章节有效，避免阻止用户操作
    isChapterValid.value = true;
  } finally {
    isValidatingChapter.value = false;
  }
};

async function tryLoadCurrentJson() {
  if (!props.projectTitle || !props.chapterTitle) return;
  const filePath = `draft/${props.projectTitle}/${props.chapterTitle}/Current.json`;
  try {
    const res = await fetch(`/api/local/read-file?path=${encodeURIComponent(filePath)}`);
    const data = await res.json();
    if (res.ok && data.success && data.content) {
      const parsed = JSON.parse(data.content);
      currentAnalyzedItems.value = parsed;
      rawLlmOutputText.value = null;
    }
  } catch (e) {
    currentAnalyzedItems.value = { eraBackground: '', characters: [], specialItems: [], techniques: [], scenes: [] };
  }
}

onMounted(() => {
  loadActivePrompt();
  if (props.projectTitle) {
    loadGlobalPresets();
    validateCurrentChapter();
    tryLoadCurrentJson();
  }
});

watch(() => props.initialText, (newVal) => {
  console.log('[GlobalReasoningDrawer] initialText变更，新长度:', newVal?.length || 0);
  inputText.value = newVal || '';
});

watch(() => props.projectTitle, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    loadActivePrompt();
    loadGlobalPresets();
    currentAnalyzedItems.value = { eraBackground: '', characters: [], specialItems: [], techniques: [], scenes: [] };
    rawLlmOutputText.value = null;
    if (error.value) error.value = null;
    showTextViewerModal.value = false;
    validateCurrentChapter();
  }
});

watch([() => props.projectTitle, () => props.chapterTitle], ([newProjectTitle, newChapterTitle]) => {
  if (newProjectTitle && newChapterTitle) {
    validateCurrentChapter();
    tryLoadCurrentJson();
  }
});

const openTextViewerModal = () => {
  console.log('[GlobalReasoningDrawer] 打开文本查看器，当前inputText长度:', inputText.value?.length || 0,
    '内容预览:', inputText.value?.substring(0, 50) || '无内容');
  showTextViewerModal.value = true;
};

const parseLLMOutputText = (textOutput) => {
  if (!textOutput || typeof textOutput !== 'string') {
    console.warn('[GlobalReasoningDrawer] LLM output is not a string or is empty.');
    return { eraBackground: '', characters: [], specialItems: [], techniques: [], scenes: [] };
  }

  const lines = textOutput.split('\n').map(line => line.trim()).filter(line => line);
  const result = {
    eraBackground: '',
    characters: [],
    specialItems: [],
    techniques: [],
    scenes: []
  };

  lines.forEach(line => {
    if (line.startsWith('时代背景:')) {
      result.eraBackground = line.substring('时代背景:'.length).trim();
    } else if (line.startsWith('{Character}')) {
      // 处理新格式的角色行: {Character} 名称 - 描述
      const content = line.substring('{Character}'.length).trim();
      const parts = content.split('-');
      if (parts.length >= 2) {
        const name = parts[0].trim();
        const description = parts.slice(1).join('-').trim();
        result.characters.push({ name, description });
      }
    } else if (line.startsWith('{sp}')) {
      // 处理新格式的特殊项行: {sp} 名称 - 描述
      const content = line.substring('{sp}'.length).trim();
      const parts = content.split('-');
      if (parts.length >= 2) {
        const name = parts[0].trim();
        const description = parts.slice(1).join('-').trim();
        result.specialItems.push({ name, description });
      }
    } else if (line.startsWith('人物:')) {
      // 兼容旧格式的角色行
      const content = line.substring('人物:'.length).trim();
      const parts = content.split('-');
      if (parts.length >= 2) {
        const name = parts[0].trim();
        const description = parts.slice(1).join('-').trim();
        result.characters.push({ name, description });
      }
    } else if (line.startsWith('特殊物品:')) {
      // 兼容旧格式的特殊物品行
      const content = line.substring('特殊物品:'.length).trim();
      const parts = content.split('-');
      if (parts.length >= 2) {
        const name = parts[0].trim();
        const description = parts.slice(1).join('-').trim();
        result.specialItems.push({ name, description });
      }
    } else if (line.startsWith('功法:')) {
      // 兼容旧格式的功法行
      const content = line.substring('功法:'.length).trim();
      const parts = content.split('-');
      if (parts.length >= 2) {
        const name = parts[0].trim();
        const description = parts.slice(1).join('-').trim();
        result.techniques.push({ name, description });
      }
    } else if (line.startsWith('场景:')) {
      // 兼容旧格式的场景行
      const content = line.substring('场景:'.length).trim();
      const parts = content.split('-');
      if (parts.length >= 2) {
        const name = parts[0].trim();
        const description = parts.slice(1).join('-').trim();
        result.scenes.push({ name, description });
      }
    } else if (line.includes(' - ')) {
      // 新增兜底：支持"名字 - 描述"格式
      const [name, ...descParts] = line.split(' - ');
      if (name && descParts.length > 0) {
        result.characters.push({
          name: name.trim(),
          description: descParts.join(' - ').trim()
        });
      }
    }
  });

  if (result.characters.length === 0 && result.specialItems.length === 0 && result.techniques.length === 0 && result.scenes.length === 0 && !result.eraBackground) {
    console.warn('[GlobalReasoningDrawer] LLM output did not match expected line-based format. Raw text will be shown.');
  }
  return result;
};

const showConflictResolver = ref(false);
const conflictType = ref('characters');
const conflictOldItems = ref([]);
const conflictNewItems = ref([]);
const pendingNewAnalyzedItems = ref(null);

const analyze = async () => {
  if (!inputText.value.trim()) {
    showErrorMessage_drawer('源文本内容为空，无法进行分析。');
    return;
  }
  if (error.value) error.value = null;
  presetImportMessage_drawer.value = '';

  // 不要重置 currentAnalyzedItems.value，这样冲突检测才能用到旧数据
  rawLlmOutputText.value = null;

  const finalPromptForAPI = currentSystemPrompt.value.replace('[TEXT_PLACEHOLDER]', inputText.value);
  console.log("--- GRD: Final Prompt being sent to LLM ---");
  console.log(finalPromptForAPI);

  try {
    const resultFromLLM = await sendTextToLLM(
      inputText.value,    // originalText
      finalPromptForAPI,  // fullPrompt (contains instructions and originalText)
      {
        analyzeType: 'custom-prompt', // Inform the type of analysis
        skipLocalAPI: true            // Force remote LLM for this specific structured output
      }
    );
    if (resultFromLLM && resultFromLLM.text) {
      rawLlmOutputText.value = resultFromLLM.text;
      console.log("--- GRD: Raw LLM text output ---");
      console.log(rawLlmOutputText.value);
      const newAnalyzed = parseLLMOutputText(resultFromLLM.text);
      console.log("--- GRD: Parsed items ---");
      console.log(JSON.stringify(newAnalyzed, null, 2));

      // 1. 角色对比
      const charOld = currentAnalyzedItems.value.characters || [];
      const charNew = newAnalyzed.characters || [];
      const charHasConflict = charNew.some(n => charOld.find(o => o.name === n.name)) || charNew.length !== charOld.length;
      if (charOld.length > 0 && charHasConflict) {
        showConflictResolver.value = true;
        conflictType.value = 'characters';
        conflictOldItems.value = charOld;
        conflictNewItems.value = charNew;
        pendingNewAnalyzedItems.value = newAnalyzed;
        return;
      }

      // 没有冲突，只更新角色相关的字段，保留其他字段
      currentAnalyzedItems.value.characters = newAnalyzed.characters || [];
      currentAnalyzedItems.value.techniques = newAnalyzed.techniques || [];
      if (newAnalyzed.eraBackground) {
        currentAnalyzedItems.value.eraBackground = newAnalyzed.eraBackground;
      }
      // 保留现有的 specialItems 和 scenes，不被智能角色覆盖
      // 自动保存分析结果到 draft/项目/章节/Current.json
      if (props.projectTitle && props.chapterTitle) {
        try {
          await saveProjectFile(
            props.projectTitle.trim(),
            'Current.json',
            currentAnalyzedItems.value,
            props.chapterTitle.trim()
          );
          console.log('[GlobalReasoningDrawer] 分析结果已自动保存到', `${props.projectTitle}/${props.chapterTitle}/Current.json`);
        } catch (saveErr) {
          console.error('[GlobalReasoningDrawer] 分析结果自动保存失败:', saveErr);
          showErrorMessage_drawer('分析结果保存失败: ' + (saveErr.message || saveErr));
        }
      } else {
        const missingFields = [];
        if (!props.projectTitle) missingFields.push('项目名称');
        if (!props.chapterTitle) missingFields.push('章节名称');
        console.warn(`[GlobalReasoningDrawer] 缺少${missingFields.join('和')}，无法自动保存分析结果`);
        showErrorMessage_drawer(`缺少${missingFields.join('和')}，无法保存分析结果`);
      }
    } else if (resultFromLLM && resultFromLLM.error) {
      // 只显示错误信息，不抛出异常，不让APP崩溃
      error.value = { message: resultFromLLM.error };
      showErrorMessage_drawer(resultFromLLM.error);
    } else {
      error.value = { message: 'LLM服务返回了空响应或无效格式。' };
      showErrorMessage_drawer('LLM服务返回了空响应或无效格式。');
    }
  } catch (e) {
    // 兜底，防止未捕获异常
    error.value = { message: e.message };
    showErrorMessage_drawer(e.message);
  }
};

const openPromptEditor = async () => {
  await loadActivePrompt();
  showPromptEditorModal.value = true;
};

const closeDrawer = () => {
  emit('update:show', false);
  emit('close-drawer');
};

const handleGlobalClassificationComplete = async (classifiedItems) => {
  try {
    const result = await onClassificationCompleteInternal(classifiedItems);
    if (result.saved) {
      showSuccessMessage_drawer(result.message || '分类后的预设已成功保存！');
    } else if (result.message) {
      showInfoMessage_drawer(result.message);
    }
  } catch (err) {
    console.error("Error after classification complete in drawer:", err);
    showErrorMessage_drawer(`处理分类结果失败: ${err.message}`);
  }
};

const handleGlobalClassificationCancel = async () => {
  try {
    const result = await onClassificationCancelInternal();
    if (result.message) {
     showInfoMessage_drawer(result.message || '预设分类已取消。');
    }
  } catch (err) {
     console.error("Error after classification cancel in drawer:", err);
     showErrorMessage_drawer(`取消分类时出错: ${err.message}`);
  }
};

const handleDeletePresetItem = async (item) => {
  console.log('[GlobalReasoningDrawer] Received delete-item event for:', item);
  try {
    const result = await deletePresetItemInternal(item);
    if (result.deleted) {
      showSuccessMessage_drawer(result.message || `预设 "${item.name}" 已删除。`);
    } else {
      showErrorMessage_drawer(result.message || `删除预设 "${item.name}" 失败。`);
    }
  } catch (err) {
    console.error('[GlobalReasoningDrawer] Error deleting preset item:', err);
    showErrorMessage_drawer(`删除预设 "${item.name}" 时出错: ${err.message}`);
  }
};

const showCharacterEditor = ref(false);
const editingCharacter = ref(null);

function handleEditPresetItem(item) {
  editingCharacter.value = { ...item };
  showCharacterEditor.value = true;
}
async function handleSaveCharacter(edited) {
  if (edited._fromCurrent) {
    // 编辑当前分析结果
    const idx = currentAnalyzedItems.value.characters.findIndex(c => c.name === edited.name);
    if (idx !== -1) currentAnalyzedItems.value.characters[idx] = { ...edited };
    // 保存
    if (props.projectTitle && props.chapterTitle) {
      try {
        await saveProjectFile(
          props.projectTitle.trim(),
          'Current.json',
          currentAnalyzedItems.value,
          props.chapterTitle.trim()
        );
        showSuccessMessage_drawer(`已更新角色"${edited.name}"并保存。`);
      } catch (e) {
        showErrorMessage_drawer('编辑角色后保存失败: ' + (e.message || e));
      }
    }
    showCharacterEditor.value = false;
    editingCharacter.value = null;
    return;
  }
  if (edited._fromSpecial) {
    const idx = currentAnalyzedItems.value.specialItems.findIndex(s => s.name === edited.name);
    if (idx !== -1) currentAnalyzedItems.value.specialItems[idx] = { ...edited };
    else currentAnalyzedItems.value.specialItems.push(edited);
    if (props.projectTitle && props.chapterTitle) {
      try {
        await saveProjectFile(
          props.projectTitle.trim(),
          'Current.json',
          currentAnalyzedItems.value,
          props.chapterTitle.trim()
        );
        showSuccessMessage_drawer(`已更新特名"${edited.name}"并保存。`);
      } catch (e) {
        showErrorMessage_drawer('编辑特名后保存失败: ' + (e.message || e));
      }
    }
    showCharacterEditor.value = false;
    editingCharacter.value = null;
    return;
  }
  // 1. 获取当前 global.json 的角色和 specials
  const current = (typeof activeGlobalPresets !== 'undefined' && activeGlobalPresets.value)
    ? activeGlobalPresets.value
    : { characters: [], specials: [] };
  const characters = [...(current.characters || [])];
  const specials = [...(current.specials || [])];

  // 2. 替换或新增角色
  const idx = characters.findIndex(c => c.name === edited.name);
  if (idx !== -1) characters[idx] = edited;
  else characters.push(edited);

  // 3. 保存到 global.json
  if (typeof saveGlobalJson === 'function') {
    await saveGlobalJson('Global.json', { characters, specials });
  }

  // 4. 刷新
  await loadGlobalPresets();
  showCharacterEditor.value = false;
  editingCharacter.value = null;
}
function handleCloseEditor() {
  showCharacterEditor.value = false;
  editingCharacter.value = null;
}

// 以下方法用于处理添加到章节请求，但我们会将其实现为空，因为您不需要这个功能
const handleRequestAddToChapter = () => {
  // 这个方法保留但不做任何事情
};

// 计算分析结果总数
const allResultItemsCount = computed(() => {
  const characters = (currentAnalyzedItems.value.characters || []).length;
  const specialItems = (currentAnalyzedItems.value.specialItems || []).length;
  const techniques = (currentAnalyzedItems.value.techniques || []).length;
  const scenes = (currentAnalyzedItems.value.scenes || []).length;
  return characters + specialItems + techniques + scenes;
});

provide('projectTitle', computed(() => props.projectTitle));

const isChapterValid = ref(true);
const isValidatingChapter = ref(false);

async function handleConflictResolve(mergedList) {
  if (!pendingNewAnalyzedItems.value) return;
  const newAnalyzed = pendingNewAnalyzedItems.value;

  if (conflictType.value === 'characters') {
    // 只更新角色相关的字段，保留其他字段
    currentAnalyzedItems.value.characters = mergedList;
    currentAnalyzedItems.value.techniques = newAnalyzed.techniques || [];
    if (newAnalyzed.eraBackground) {
      currentAnalyzedItems.value.eraBackground = newAnalyzed.eraBackground;
    }
    // 保留现有的 specialItems 和 scenes，不被智能角色覆盖
  }
  // 保存到 current.json
  if (props.projectTitle && props.chapterTitle) {
    try {
      await saveProjectFile(
        props.projectTitle.trim(),
        'Current.json',
        currentAnalyzedItems.value,
        props.chapterTitle.trim()
      );
      console.log('[GlobalReasoningDrawer] 冲突合并后已保存到', `${props.projectTitle}/${props.chapterTitle}/Current.json`);
    } catch (saveErr) {
      console.error('[GlobalReasoningDrawer] 冲突合并保存失败:', saveErr);
      showErrorMessage_drawer('冲突合并保存失败: ' + (saveErr.message || saveErr));
    }
  }
  showConflictResolver.value = false;
  pendingNewAnalyzedItems.value = null;
}
function handleConflictCancel() {
  showConflictResolver.value = false;
  pendingNewAnalyzedItems.value = null;
}

const TABS = {
  ANALYSIS: 'analysis',
  PRESET: 'preset',
  SPECIAL: 'special',
  SCENE: 'scene'
};
const activeTab = ref(TABS.ANALYSIS);

// 编辑当前分析角色
function handleEditCurrentCharacter(item) {
  editingCharacter.value = { ...item };
  showCharacterEditor.value = true;
  // 标记是编辑当前分析结果而非预设
  editingCharacter.value._fromCurrent = true;
}
// 删除当前分析角色
async function handleDeleteCurrentCharacter(item) {
  const idx = currentAnalyzedItems.value.characters.findIndex(c => c.name === item.name);
  if (idx !== -1) {
    currentAnalyzedItems.value.characters.splice(idx, 1);
    // 保存到 Current.json
    if (props.projectTitle && props.chapterTitle) {
      try {
        await saveProjectFile(
          props.projectTitle.trim(),
          'Current.json',
          currentAnalyzedItems.value,
          props.chapterTitle.trim()
        );
        showSuccessMessage_drawer(`已删除角色"${item.name}"并保存。`);
      } catch (e) {
        showErrorMessage_drawer('删除角色后保存失败: ' + (e.message || e));
      }
    }
  }
}

async function handleSmartNaming() {
  if (!inputText.value.trim()) {
    showErrorMessage_drawer('源文本内容为空，无法进行智能特名分析。');
    return;
  }
  if (error.value) error.value = null;
  presetImportMessage_drawer.value = '';

  // 设置独立的loading状态
  isSmartNamingLoading.value = true;

  try {
    // 获取 specialNaming prompt
    let prompt = promptsMap.specialNaming || defaultPromptsMap.specialNaming || '';
    if (!prompt) {
      showErrorMessage_drawer('未找到智能特名的Prompt模板。');
      isSmartNamingLoading.value = false;
      return;
    }
    const finalPrompt = prompt.replace('[TEXT_PLACEHOLDER]', inputText.value);

    const result = await sendTextToLLMIndependent(
      inputText.value,
      finalPrompt,
      {
        analyzeType: 'custom-prompt',
        skipLocalAPI: true
      }
    );
    if (result && result.text) {
      // 解析每行"名称 - 英文描述"
      const lines = result.text.split('\n').map(l => l.trim()).filter(l => l && l.includes(' - '));
      const specials = lines.map(line => {
        const [name, ...descParts] = line.split(' - ');
        return { name: name.trim(), description: descParts.join(' - ').trim(), type: 'special' };
      });
      currentAnalyzedItems.value.specialItems = specials;
      // 保存到 Current.json
      if (props.projectTitle && props.chapterTitle) {
        try {
          await saveProjectFile(
            props.projectTitle.trim(),
            'Current.json',
            currentAnalyzedItems.value,
            props.chapterTitle.trim()
          );
          showSuccessMessage_drawer('智能特名分析结果已保存。');
        } catch (e) {
          showErrorMessage_drawer('智能特名结果保存失败: ' + (e.message || e));
        }
      }
      // 自动切换到特名Tab
      activeTab.value = TABS.SPECIAL;
    } else if (result && result.error) {
      showErrorMessage_drawer(result.error);
    } else {
      showErrorMessage_drawer('LLM服务返回了空响应或无效格式。');
    }
  } catch (e) {
    showErrorMessage_drawer(e.message);
  } finally {
    isSmartNamingLoading.value = false;
  }
}

async function handleSmartScene() {
  // 设置独立的loading状态
  isSmartSceneLoading.value = true;

  try {
    // 只影响智能场景，不影响其它功能
    console.log('[handleSmartScene] 检查项目数据:', projectContext.projectData?.value);
    console.log('[handleSmartScene] 检查行数据:', projectContext.projectData?.value?.data?.rows);

    // 尝试从项目上下文获取数据
    let rows = projectContext.projectData?.value?.data?.rows;

    // 如果项目上下文中没有数据，尝试直接从文件系统加载
    if (!rows || rows.length === 0) {
      console.log('[handleSmartScene] 项目上下文中没有行数据，尝试从文件系统加载');

      if (!props.projectTitle || !props.chapterTitle) {
        showErrorMessage_drawer('缺少项目名称或章节名称，无法进行智能场景分析。');
        isSmartSceneLoading.value = false;
        return;
      }

      try {
        // 尝试从project-data.json文件加载数据
        const projectDataPath = `draft/${props.projectTitle}/${props.chapterTitle}/project-data.json`;
        const response = await fetch(`/api/local/read-file?path=${encodeURIComponent(projectDataPath)}`);

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.content) {
            const projectData = JSON.parse(result.content);
            rows = projectData.rows;
            console.log('[handleSmartScene] 从文件系统加载到行数据:', rows?.length || 0);
          }
        }
      } catch (error) {
        console.error('[handleSmartScene] 从文件系统加载数据失败:', error);
      }
    }

    if (!rows || rows.length === 0) {
      showErrorMessage_drawer('未找到原始SRT行数据，无法进行智能场景分析。请确保已上传SRT文件并加载了项目数据。');
      isSmartSceneLoading.value = false;
      return;
    }
    if (error.value) error.value = null;
    presetImportMessage_drawer.value = '';
    // 获取 smartScene prompt
    let prompt = promptsMap.smartScene || defaultPromptsMap.smartScene || '';
    if (!prompt) {
      showErrorMessage_drawer('未找到智能场景的Prompt模板。');
      isSmartSceneLoading.value = false;
      return;
    }
    // 只取未合并的原始行
    const filteredRows = rows.filter(row => !row.isMerged);
    // 生成带编号的文本
    const numberedText = filteredRows.map((row, idx) => `${idx + 1}. ${row.description.replace(/\n/g, ' ')}`).join('\n');
    // 注入 prompt
    const finalPrompt = prompt.replace('{textContent}', numberedText);
    // 日志：输出 prompt 和原始文本段落数
    console.log('[handleSmartScene] 发送给LLM的finalPrompt:', finalPrompt);
    console.log(`[handleSmartScene] 原始文本段落数: ${rows.length}`);

    const result = await sendTextToLLMIndependent(
      numberedText, // 传递原始带编号文本
      finalPrompt,
      {
        analyzeType: 'custom-prompt',
        skipLocalAPI: true
      }
    );
    if (result && result.text) {
      // 解析 LLM 返回的场景结果
      const lines = result.text.split(/\n|\r/).map(l => l.trim());
      const scenes = [];
      let cur = {};
      lines.forEach(line => {
        if (/^Line Range:/i.test(line)) {
          if (cur.range || cur.location || cur.time) { scenes.push(cur); cur = {}; }
          cur.range = line.replace(/^Line Range:/i, '').trim();
        } else if (/^Location:/i.test(line)) {
          cur.location = line.replace(/^Location:/i, '').trim();
        } else if (/^Time:/i.test(line)) {
          cur.time = line.replace(/^Time:/i, '').trim();
        } else if (/^---+$/.test(line)) {
          if (cur.range || cur.location || cur.time) { scenes.push(cur); cur = {}; }
        }
      });
      if (cur.range || cur.location || cur.time) scenes.push(cur);
      currentAnalyzedItems.value.scenes = scenes;
      // 保存到 Current.json
      if (props.projectTitle && props.chapterTitle) {
        try {
          await saveProjectFile(
            props.projectTitle.trim(),
            'Current.json',
            currentAnalyzedItems.value,
            props.chapterTitle.trim()
          );
          showSuccessMessage_drawer('智能场景分析结果已保存。');
        } catch (e) {
          showErrorMessage_drawer('智能场景结果保存失败: ' + (e.message || e));
        }
      }
      // 自动切换到场景Tab
      activeTab.value = TABS.SCENE;
    } else if (result && result.error) {
      showErrorMessage_drawer(result.error);
    } else {
      showErrorMessage_drawer('LLM服务返回了空响应或无效格式。');
    }
  } catch (e) {
    showErrorMessage_drawer(e.message);
  } finally {
    isSmartSceneLoading.value = false;
  }
}

// 多 prompt 类型支持
const promptTypeOptions = {
  globalReasoning: '角色造型',
  specialNaming: '特别名称',
  smartScene: '智能场景'
};
const currentPromptType = ref('globalReasoning');
const promptsMap = reactive({
  globalReasoning: '',
  specialNaming: '',
  smartScene: ''
});
const defaultPromptsMap = reactive({
  globalReasoning: '',
  specialNaming: '',
  smartScene: ''
});

// 加载 prompts.json
async function loadAllPrompts() {
  const res = await fetch('/userdata/prompts.json');
  if (!res.ok) return;
  const data = await res.json();
  // 角色造型
  promptsMap.globalReasoning = (data.globalReasoning?.user || '').join ? data.globalReasoning.user.join('\n') : (data.globalReasoning?.user || '');
  defaultPromptsMap.globalReasoning = (data.globalReasoning?.default || '').join ? data.globalReasoning.default.join('\n') : (data.globalReasoning?.default || '');
  // 特别名称
  promptsMap.specialNaming = (data.specialNaming?.user || '').join ? data.specialNaming.user.join('\n') : (data.specialNaming?.user || '');
  defaultPromptsMap.specialNaming = (data.specialNaming?.default || '').join ? data.specialNaming.default.join('\n') : (data.specialNaming?.default || '');
  // 智能场景
  promptsMap.smartScene = (data.smartScene?.user || '').join ? data.smartScene.user.join('\n') : (data.smartScene?.user || '');
  defaultPromptsMap.smartScene = (data.smartScene?.default || '').join ? data.smartScene.default.join('\n') : (data.smartScene?.default || '');
}

onMounted(() => {
  loadAllPrompts();
});

function handlePromptTypeChange(type) {
  currentPromptType.value = type;
}

async function handleSavePrompt({ type, content }) {
  // 保存到 prompts.json
  const res = await fetch('/api/local/save-userdata-prompts', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      ...JSON.parse(JSON.stringify(await (await fetch('/userdata/prompts.json')).json())),
      [type]: {
        ...(promptsMap[type] ? { user: content.split('\n') } : {}),
        ...(defaultPromptsMap[type] ? { default: defaultPromptsMap[type].split('\n') } : {})
      }
    })
  });
  if (res.ok) {
    promptsMap[type] = content;
  }
}

function handleEditSpecial(item) {
  editingCharacter.value = { ...item, _fromSpecial: true };
  showCharacterEditor.value = true;
}
async function handleDeleteSpecial(item) {
  const idx = currentAnalyzedItems.value.specialItems.findIndex(s => s.name === item.name);
  if (idx !== -1) {
    currentAnalyzedItems.value.specialItems.splice(idx, 1);
    // 保存到 Current.json
    if (props.projectTitle && props.chapterTitle) {
      try {
        await saveProjectFile(
          props.projectTitle.trim(),
          'Current.json',
          currentAnalyzedItems.value,
          props.chapterTitle.trim()
        );
        showSuccessMessage_drawer(`已删除特名"${item.name}"并保存。`);
      } catch (e) {
        showErrorMessage_drawer('删除特名后保存失败: ' + (e.message || e));
      }
    }
  }
}
</script>

<style scoped>
.reasoning-drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #2a273f; /* Dark theme background */
}

.drawer-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background-color: #232136; /* Darker shade for header */
  border-bottom: 1px solid #44415a; /* Dark theme border */
  position: relative;
}

.drawer-header h3 {
  margin: 0;
  font-size: 1.25em;
  color: #e0def4;
  font-weight: 600;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  margin-left: -30px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1;
  margin-left: auto;
}

.icon-button {
  background: none;
  border: none;
  color: #908caa;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.icon-button:hover {
  background-color: #44415a;
  color: #e0def4;
}

.close-drawer-button {
  background: none;
  border: none;
  font-size: 1.7em;
  font-weight: bold;
  color: #908caa;
  cursor: pointer;
  padding: 0 8px;
}
.close-drawer-button:hover {
  color: #e0def4;
}

.drawer-body {
  flex-grow: 1;
  overflow-y: auto;
  padding: 20px;
  color: #e0def4;
  display: flex; /* Added for overall body flex control */
  flex-direction: column; /* Stack top actions and panels vertically */
}

.top-action-buttons-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* Prevent this from shrinking if panels grow */
}

.analyze-button,
.import-preset-button {
  padding: 10px 20px;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  font-weight: 500;
  transition: background-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 180px;
  justify-content: center;
}

.analyze-button i,
.import-preset-button i {
  font-size: 1.2em;
  line-height: 1;
}

.analyze-button {
  background-color: #3e8fb0;
}
.analyze-button:hover {
  background-color: #4ea8c6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
.analyze-button:disabled {
  background-color: #5c6370;
  cursor: not-allowed;
  opacity: 0.7;
}

.import-preset-button {
  background-color: #555c6b;
}
.import-preset-button:hover {
  background-color: #666f7d;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
.import-preset-button:disabled {
  background-color: #4a4f58;
  cursor: not-allowed;
  opacity: 0.7;
}

.spinner {
  animation: rotate 2s linear infinite;
  width: 18px;
  height: 18px;
  margin-right: 5px;
}

.path {
  stroke: #fff;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotate {
  100% { transform: rotate(360deg); }
}

@keyframes dash {
  0% { stroke-dasharray: 1, 150; stroke-dashoffset: 0; }
  50% { stroke-dasharray: 90, 150; stroke-dashoffset: -35; }
  100% { stroke-dasharray: 90, 150; stroke-dashoffset: -124; }
}

.message.drawer-message {
  padding: 10px 15px;
  margin-top: 0; /* Removed top margin as it's handled by container */
  margin-bottom: 15px; /* Spacing below messages */
  border-radius: 4px;
  font-size: 0.9em;
  text-align: left;
  flex-shrink: 0; /* Prevent messages from shrinking */
}
.message-success {
  background-color: #27522b;
  color: #a7d7ab;
  border: 1px solid #3a7441;
}
.message-error { /* This class might be unused now as panel has its own */
  background-color: #5a2a2a;
  color: #f8b4b4;
  border: 1px solid #8a4747;
}
.message-info {
  background-color: #2a3b4d;
  color: #a9c3e0;
  border: 1px solid #425a78;
}

/* Panels Layout */
.panels-layout-container {
  display: flex;
  gap: 20px; /* Space between the two main panels */
  flex-grow: 1; /* Allow this container to take up remaining vertical space */
  min-height: 0; /* Important for flex children with overflow-y: auto */
}

.panel {
  flex: 1; /* Each panel takes half the space by default */
  display: flex;
  flex-direction: column; /* Stack title and content vertically */
  min-width: 0; /* For flex items to shrink properly */
  background-color: #232136; /* Base background for panels */
  border-radius: 6px;
  /* Removed border and padding from here, individual panels will manage their content area */
}

.current-analysis-panel {
  /* Specific styles if current analysis panel needs to differ from preset panel wrapper */
}

.preset-manager-panel-wrapper { /* Renamed from preset-panel-container to avoid confusion */
  /* This wrapper will hold the title and the PresetManagerPanel component */
}

.panel-title { /* Common style for titles of both panels */
  font-size: 1.15em;
  color: #e0def4;
  font-weight: 600;
  margin: 0; /* Remove default margin */
  padding: 15px 20px; /* Add padding to title area */
  border-bottom: 1px solid #44415a;
  flex-shrink: 0; /* Prevent title from shrinking */
}

/* Ensure child components (CurrentAnalysisResultsPanel and PresetManagerPanel) fill their parent .panel */
/* CurrentAnalysisResultsPanel already has flex:1 and overflow-y:auto for its content */
/* PresetManagerPanel might need similar styling if its content is expected to scroll independently within its .panel */
/* The .preset-panel-container (now preset-manager-panel-wrapper) has background and radius */
/* The actual PresetManagerPanel component has its own internal styling for the table, which is fine. */

/* Style for .preset-title which is specific to the preset panel's h4 */
.preset-title {
  /* panel-title already covers most of this, this is now more of a marker if unique styles are needed */
}

.tab-nav-bar {
  display: flex;
  justify-content: center;
  gap: 1.2rem;
  margin-bottom: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}
.tab-nav-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #2a273f;
  color: #a9adc1;
  border: 1.5px solid #44415a;
  border-radius: 8px;
  font-size: 1.08em;
  font-weight: 600;
  padding: 0.7em 2.2em;
  cursor: pointer;
  transition: background 0.18s, color 0.18s, border 0.18s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}
.tab-nav-btn.active {
  background: #3e8fb0;
  color: #fff;
  border-color: #3e8fb0;
  box-shadow: 0 4px 16px rgba(62,143,176,0.10);
}
.tab-nav-btn:hover:not(.active) {
  background: #393552;
  color: #e0def4;
  border-color: #5c6370;
}
.tab-nav-divider {
  height: 1.5px;
  background: #44415a;
  margin: 0 0 18px 0;
  border-radius: 1px;
}
.tab-content .panel {
  width: 100%;
  min-width: 0;
  background: #232136;
  border-radius: 0 0 8px 8px;
  padding: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.small-action-btn {
  padding: 6px 16px !important;
  font-size: 0.98em !important;
  min-width: 110px;
  border-radius: 6px;
  margin-right: 8px;
}
.smart-naming-btn {
  background: #3e8fb0;
  color: #fff;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background 0.18s, color 0.18s;
  display: flex;
  align-items: center;
  gap: 6px;
}
.smart-naming-btn:hover {
  background: #4ea8c6;
  color: #fff;
}
</style>