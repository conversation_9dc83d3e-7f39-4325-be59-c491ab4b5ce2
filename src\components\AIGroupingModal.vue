<template>
  <BaseModal
    :show="show"
    @update:show="$emit('update:show', false)"
    title="AI智能分组"
  >
    <template #title>
      <div class="modal-title-bar">
        <span>AI智能分组</span>
        <button class="prompt-setting-btn small" @click.stop="showPromptEditorModal = true">
          <i class="ri-edit-line" /> 提示词设置
        </button>
      </div>
    </template>
    <div class="ai-grouping-modal">
      <div class="modal-section info-section">
        <div class="section-title">
          <i class="ri-information-line" />
          <span>使用说明</span>
        </div>
        <p class="section-description">
          AI分组功能将分析当前字幕内容，自动识别相关句子并进行合并分组。系统会考虑句子上下文、语义关联和叙事逻辑等因素，智能推荐合并方案。
        </p>
      </div>

      <div class="modal-section parameters-section">
        <div class="section-title">
          <i class="ri-settings-line" />
          <span>参数设置</span>
        </div>
        <div class="parameters-form">
          <div class="form-group">
            <label>分组策略：</label>
            <input
              type="text"
              class="form-control"
              value="语义相关度 (AI自动)"
              readonly
            >
          </div>

          <div class="form-group">
            <label>最大组句数：</label>
            <select
              v-model="maxGroupSize"
              class="form-control"
            >
              <option value="2">
                2 句/组
              </option>
              <option value="3">
                3 句/组
              </option>
              <option value="4">
                4 句/组
              </option>
              <option value="5">
                5 句/组
              </option>
              <option value="0">
                动态 (AI判断)
              </option>
            </select>
          </div>
        </div>
      </div>

      <div
        v-if="showAiResponse"
        class="modal-section ai-response-section"
      >
        <div class="section-title">
          <i class="ri-robot-line" />
          <span>AI回应</span>
        </div>
        <div class="ai-response-content">
          <pre>{{ aiResponseText }}</pre>
        </div>
      </div>

      <div class="modal-section status-section">
        <div
          v-if="isProcessing"
          class="processing-status"
        >
          <div class="loading-spinner" />
          <div class="status-message">
            <div class="status-title">
              处理中...
            </div>
            <div class="status-detail">
              {{ statusMessage }}
            </div>

            <!-- 分批处理进度条 -->
            <div v-if="batchProgress.isBatchMode && batchProgress.totalBatches > 1" class="batch-progress">
              <div class="progress-info">
                <span>分批处理进度: {{ batchProgress.currentBatch }}/{{ batchProgress.totalBatches }} 批次</span>
                <span>{{ batchProgress.processedCount }}/{{ batchProgress.totalCount }} 条字幕</span>
              </div>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: `${(batchProgress.processedCount / batchProgress.totalCount) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-else-if="resultReady"
          class="result-status"
        >
          <div class="status-icon success">
            <i class="ri-check-line" />
          </div>
          <div class="status-message">
            <div class="status-title">
              分组完成！
            </div>
            <div class="status-detail">
              AI已创建分组方案，预计合并 {{ getMergedGroupsCount() }} 组。
            </div>
          </div>
        </div>

        <div
          v-else-if="error"
          class="error-status"
        >
          <div class="status-icon error">
            <i class="ri-error-warning-line" />
          </div>
          <div class="status-message">
            <div class="status-title">
              处理失败
            </div>
            <div class="status-detail">
              {{ error }}
            </div>
          </div>
        </div>
      </div>
      <div v-if="warningMessage" class="ai-warning-bar">{{ warningMessage }}</div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <button
          class="footer-button cancel"
          @click="$emit('update:show', false)"
        >
          取消
        </button>
        <button
          v-if="!isProcessing && !resultReady"
          class="footer-button primary"
          @click="processGrouping"
        >
          开始分组
        </button>
        <button
          v-if="resultReady"
          class="footer-button primary"
          @click="applyGrouping"
        >
          应用分组
        </button>
      </div>
    </template>
  </BaseModal>

  <!-- 新增：PromptEditorModal 弹窗 -->
  <PromptEditorModal
    :show="showPromptEditorModal"
    :promptsMap="promptsMap"
    :defaultPromptsMap="defaultPromptsMap"
    :currentType="'aiGrouping'"
    :promptTypeOptions="{ aiGrouping: 'AI分组' }"
    @update:show="showPromptEditorModal = $event"
    @save-prompt="handleSavePrompt"
  />
</template>

<script>
import { ref, watch, onMounted, reactive } from 'vue';
import BaseModal from './BaseModal.vue';
import PromptEditorModal from './PromptEditorModal.vue';
import llmService from '../services/llmService';

// 保存 aiGrouping.user 到 prompts.json
async function saveAiGroupingPrompt(newPrompt) {
  try {
    // 先获取原始 prompts.json
    const res = await fetch('/userdata/prompts.json');
    if (!res.ok) throw new Error('无法加载 prompts.json');
    const data = await res.json();
    // 更新 aiGrouping.user
    if (!data.aiGrouping) data.aiGrouping = {};
    // 存为多行数组
    data.aiGrouping.user = newPrompt.split(/\r?\n/);
    // 写回 prompts.json
    const saveRes = await fetch('/api/local/save-userdata-prompts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data, null, 2)
    });
    if (!saveRes.ok) throw new Error('保存 prompts.json 失败');
    return true;
  } catch (e) {
    console.error('保存 aiGrouping prompt 失败:', e);
    throw e;
  }
}

export default {
  name: 'AIGroupingModal',
  components: {
    BaseModal,
    PromptEditorModal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    subtitles: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:show', 'apply-grouping'],
  setup(props, { emit }) {
    // 参数设置
    const groupingStrategy = ref('semantic');
    const maxGroupSize = ref('3');
    const groupingDirection = ref('auto');

    // 处理状态
    const isProcessing = ref(false);
    const resultReady = ref(false);
    const error = ref('');
    const statusMessage = ref('准备中...');
    const groupingResult = ref([]);

    // 分批处理进度状态
    const batchProgress = ref({
      currentBatch: 0,
      totalBatches: 0,
      processedCount: 0,
      totalCount: 0,
      isBatchMode: false
    });

    // AI响应展示
    const showAiResponse = ref(false);
    const aiResponseText = ref('');

    // 提示词编辑器
    const showPromptEditorModal = ref(false);
    const customPrompt = ref('');
    const DEFAULT_PROMPT_TEMPLATE = ref('');

    // 新的prompt管理方式，兼容PromptEditorModal
    const promptsMap = reactive({
      aiGrouping: ''
    });
    const defaultPromptsMap = reactive({
      aiGrouping: ''
    });

    // 新增：警告消息
    const warningMessage = ref('');
    let warningTimer = null;

    // 动态加载prompt（优先用户自定义，无则用默认）
    onMounted(async () => {
      try {
        const res = await fetch('/userdata/prompts.json');
        if (!res.ok) throw new Error('无法加载 prompts.json');
        const data = await res.json();

        // 设置默认prompt
        if (Array.isArray(data.aiGrouping?.default)) {
          DEFAULT_PROMPT_TEMPLATE.value = data.aiGrouping.default.join('\n');
          defaultPromptsMap.aiGrouping = data.aiGrouping.default.join('\n');
        } else if (typeof data.aiGrouping?.default === 'string') {
          DEFAULT_PROMPT_TEMPLATE.value = data.aiGrouping.default;
          defaultPromptsMap.aiGrouping = data.aiGrouping.default;
        }

        // 设置用户prompt，优先用户自定义，无则用默认
        if (data.aiGrouping.user && Array.isArray(data.aiGrouping.user) && data.aiGrouping.user.length > 0) {
          customPrompt.value = data.aiGrouping.user.join('\n');
          promptsMap.aiGrouping = data.aiGrouping.user.join('\n');
        } else if (typeof data.aiGrouping.user === 'string' && data.aiGrouping.user.trim()) {
          customPrompt.value = data.aiGrouping.user;
          promptsMap.aiGrouping = data.aiGrouping.user;
        } else {
          customPrompt.value = DEFAULT_PROMPT_TEMPLATE.value;
          promptsMap.aiGrouping = DEFAULT_PROMPT_TEMPLATE.value;
        }
      } catch (e) {
        console.error('加载 aiGrouping prompt 失败:', e);
        DEFAULT_PROMPT_TEMPLATE.value = 'Prompt not found';
        customPrompt.value = 'Prompt not found';
        defaultPromptsMap.aiGrouping = 'Prompt not found';
        promptsMap.aiGrouping = 'Prompt not found';
      }
    });

    // 监听最大组句数变化，重置结果
    watch(maxGroupSize, () => {
      if (resultReady.value) {
        resultReady.value = false;
        groupingResult.value = [];
      }
    });

    // 监听提示词变化，重置结果
    watch(customPrompt, () => {
      if (resultReady.value) {
        resultReady.value = false;
        groupingResult.value = [];
      }
    });

    // 计算将合并的组数
    const getMergedGroupsCount = () => {
      if (!groupingResult.value) return 0;

      // 处理不同格式的分组结果
      if (typeof groupingResult.value === 'string') {
        // 字符串格式，无法计算合并组数
        return '未知';
      } else if (groupingResult.value.groups && Array.isArray(groupingResult.value.groups)) {
        // 包含groups字段的对象格式
        return groupingResult.value.groups.filter(group => Array.isArray(group) && group.length > 1).length;
      } else if (Array.isArray(groupingResult.value)) {
        // 数组格式
        return groupingResult.value.filter(group => Array.isArray(group) && group.length > 1).length;
      } else if (groupingResult.value.rawText) {
        // 只有原始文本的格式，无法准确计算
        return '未知';
      }

      return 0;
    };

    // 重置提示词为默认值
    const resetPrompt = async () => {
      try {
        const res = await fetch('/userdata/prompts.json');
        if (!res.ok) throw new Error('无法加载 prompts.json');
        const data = await res.json();
        let def = '';
        if (Array.isArray(data.aiGrouping?.default)) {
          def = data.aiGrouping.default.join('\n');
        } else if (typeof data.aiGrouping?.default === 'string') {
          def = data.aiGrouping.default;
        }
        customPrompt.value = def;
      } catch {
        customPrompt.value = DEFAULT_PROMPT_TEMPLATE.value;
      }
    };

    // 保存弹窗编辑的prompt
    const handleSavePrompt = async ({ type, content }) => {
      if (type === 'aiGrouping') {
        customPrompt.value = content;
        promptsMap.aiGrouping = content;
        try {
          await saveAiGroupingPrompt(content);
        } catch (e) {
          // 可加错误提示
          console.error('保存AI分组prompt失败:', e);
        }
      }
    };

    // 获取用户设置中的LLM配置
    const getUserLLMSettings = async () => {
      try {
        console.log('开始获取用户LLM设置...');

        // 1. 首先从服务器API获取，这应该是最准确的来源
        try {
          console.log('尝试从服务器API获取设置...');
          const response = await fetch('/api/local/load-user-settings?filename=usersettings.json');
          if (response.ok) {
            const result = await response.json();
            if (result.success && result.settings && result.settings.llm) {
              console.log('从服务器API获取到用户LLM设置:', {
                provider: result.settings.llm.provider,
                model: result.settings.llm.defaultModel,
                hasKey: !!getApiKeyForUserSettings(result.settings.llm)
              });
              return result.settings.llm;
            }
          }
        } catch (err) {
          console.warn('从服务器API获取设置失败:', err);
        }

        // 2. 尝试从localStorage读取设置
        const settingsStr = localStorage.getItem('usersettings');
        if (settingsStr) {
          const settings = JSON.parse(settingsStr);
          if (settings && settings.llm) {
            console.log('从localStorage获取到用户LLM设置:', {
              provider: settings.llm.provider,
              model: settings.llm.defaultModel,
              hasKey: !!getApiKeyForUserSettings(settings.llm)
            });
            return settings.llm;
          }
        }

        // 如果都失败，返回null
        console.warn('未能获取有效的用户LLM设置，将使用默认设置');
        return null;
      } catch (err) {
        console.error('获取用户LLM设置失败:', err);
        return null;
      }
    };

    // 辅助函数，检查用户设置中是否有有效的API密钥
    const getApiKeyForUserSettings = (llmSettings) => {
      if (!llmSettings) return null;

      switch(llmSettings.provider) {
        case 'google': return llmSettings.googleApiKey;
        case 'openrouter': return llmSettings.openrouterApiKey;
        case 'openai': return llmSettings.openaiApiKey;
        case 'anthropic': return llmSettings.anthropicApiKey;
        default: return null;
      }
    };

    // 处理分组流程
    const processGrouping = async () => {
      if (!props.subtitles || props.subtitles.length === 0) {
        error.value = '没有可用的字幕内容';
        return;
      }

      // 重置状态
      isProcessing.value = true;
      resultReady.value = false;
      error.value = '';
      statusMessage.value = '正在分析字幕内容...';
      showAiResponse.value = false;
      aiResponseText.value = '';

      try {
        // 准备要处理的字幕数据
        const subtitleData = props.subtitles.map(item => ({
          id: item.id,
          content: item.content,
          originalIndex: item.originalIndex,
          isMerged: item.isMerged
        }));

        console.log('准备处理字幕数据:', subtitleData.length, '条');

        // 设置分批进度
        batchProgress.value = {
          currentBatch: 0,
          totalBatches: 0,
          processedCount: 0,
          totalCount: subtitleData.length,
          isBatchMode: subtitleData.length > 50
        };

        statusMessage.value = '正在进行智能语义分析...';

        // 获取用户设置的LLM
        const userLLMSettings = await getUserLLMSettings();
        console.log('获取到的用户LLM设置:', userLLMSettings ? {
          provider: userLLMSettings.provider,
          model: userLLMSettings.defaultModel,
          temperature: userLLMSettings.temperature
        } : '未获取到有效设置');

        // 进度回调函数
        const onProgress = (progress) => {
          batchProgress.value = {
            ...batchProgress.value,
            ...progress
          };

          if (progress.totalBatches > 1) {
            statusMessage.value = `正在处理第 ${progress.currentBatch}/${progress.totalBatches} 批次 (${progress.processedCount}/${progress.totalCount} 条字幕)`;
          } else {
            statusMessage.value = '正在进行智能语义分析...';
          }
        };

        const llmOptions = {
          forceUseApi: true,
          userSettings: userLLMSettings, // 传递用户设置的LLM配置
          onProgress: onProgress // 添加进度回调
        };

        console.log('传递给llmService的选项:', {
          forceUseApi: llmOptions.forceUseApi,
          userSettings: llmOptions.userSettings ? {
            provider: llmOptions.userSettings.provider,
            model: llmOptions.userSettings.defaultModel
          } : null,
          maxGroupSize: maxGroupSize.value
        });

        let response;

        // 判断是使用默认提示词还是自定义提示词
        const useCustomPrompt = customPrompt.value && customPrompt.value.trim() !== DEFAULT_PROMPT_TEMPLATE.value.trim();
        if (useCustomPrompt) {
          // 使用自定义提示词
          console.log('使用自定义提示词');

          // 准备提示词
          const textContent = subtitleData.map(item => item.content).join('\n');
          const numberOfLines = subtitleData.length;

          let primaryLengthTarget = "2 or 3 subtitles";
          const userMaxGroupSize = parseInt(maxGroupSize.value);
          if (userMaxGroupSize > 0) {
            primaryLengthTarget = `approximately ${userMaxGroupSize} subtitles`;
            if (userMaxGroupSize === 1) {
              primaryLengthTarget = "1 subtitle (but only for exceptionally impactful lines)";
            }
          }

          // 替换模板变量
          let finalPrompt = customPrompt.value
            .replace(/\{primaryLengthTarget\}/g, primaryLengthTarget)
            .replace(/\{numberOfLines\}/g, numberOfLines)
            .replace(/\{textContent\}/g, textContent);

          response = await llmService.performCustomPrompting(subtitleData, finalPrompt, llmOptions);
        } else {
          // 使用默认提示词
          console.log('使用默认提示词');
          response = await llmService.performSemanticGrouping(subtitleData, {
            ...llmOptions,
            maxGroupSize: maxGroupSize.value,
            groupingDirection: groupingDirection.value
          });
        }

        // 即使LLM调用失败并回退到mock，performSemanticGrouping应该也会返回一个groupingResult
        // 所以我们在这里检查response和response.groupingResult的有效性
        if (response && response.groupingResult && response.groupingResult.length > 0) {
          // 保存分组结果和原始文本
          groupingResult.value = response.groupingResult;

          // 如果有原始文本，也保存下来
          if (response.rawText) {
            console.log('保存AI返回的原始文本格式');
            // 将原始文本保存到响应中，以便在应用时使用
            groupingResult.value = {
              groups: response.groupingResult,
              rawText: response.rawText
            };
          }

          resultReady.value = true; // 确保在获得有效结果（包括mock）后设置
          console.log('AI分组成功或已回退到模拟分组。');
        } else {
          // 如果response无效或没有分组结果，则视为错误
          console.error('AI分组未能返回有效结果，即使是模拟结果。');
          error.value = 'AI未能生成分组方案，请稍后重试或检查字幕内容。';
          resultReady.value = false; // 明确设置为false
        }

        // 显示AI回应（如果有）
        if (response && response.originalResponse) {
          showAiResponse.value = true;
          aiResponseText.value = response.originalResponse;
        }
      } catch (err) {
        console.error('AI分组处理失败:', err);
        error.value = err.message || '处理请求时发生错误';
        // 新增 warning 逻辑
        warningMessage.value = error.value;
        if (warningTimer) clearTimeout(warningTimer);
        warningTimer = setTimeout(() => { warningMessage.value = ''; }, 3000);
        // 不再返回任何分组结果
      } finally {
        isProcessing.value = false;
      }
    };

    // 应用分组结果
    const applyGrouping = () => {
      if (!resultReady.value || !groupingResult.value) {
        return;
      }

      let result = groupingResult.value;

      // 处理不同格式的分组结果
      if (typeof result === 'string') {
        // 如果是字符串格式，直接作为原始文本传递
        console.log('应用字符串格式的AI分组结果');
        emit('apply-grouping', { rawText: result });
      } else if (result.rawText) {
        // 如果包含rawText字段，优先使用原始文本格式
        console.log('应用带有原始文本的AI分组结果');
        emit('apply-grouping', { rawText: result.rawText });
      } else if (result.groups && Array.isArray(result.groups)) {
        // 如果包含groups字段，使用groups数组
        console.log('应用groups数组格式的AI分组结果');
        emit('apply-grouping', result.groups);
      } else if (Array.isArray(result)) {
        // 兼容原有数组格式
        console.log('应用数组格式的AI分组结果');
        emit('apply-grouping', result);
      } else {
        console.error('无法识别的分组结果格式:', result);
        return;
      }

      emit('update:show', false);
    };

    return {
      groupingStrategy,
      maxGroupSize,
      groupingDirection,
      isProcessing,
      resultReady,
      error,
      statusMessage,
      groupingResult,
      showAiResponse,
      aiResponseText,
      showPromptEditorModal,
      customPrompt,
      DEFAULT_PROMPT_TEMPLATE,
      promptsMap,
      defaultPromptsMap,
      batchProgress,
      processGrouping,
      applyGrouping,
      getMergedGroupsCount,
      resetPrompt,
      handleSavePrompt,
      warningMessage,
    };
  }
};
</script>

<style scoped>
.ai-grouping-modal {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal-section {
  background-color: #232136;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #44415a;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  font-weight: 600;
  color: #e0def4;
  margin-bottom: 12px;
}

.section-title i {
  color: #3e8fb0;
}

.section-title .toggle-button {
  margin-left: auto;
  padding: 2px 8px;
  font-size: 0.8rem;
  background-color: #2a273f;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  cursor: pointer;
}

.section-title .toggle-button:hover {
  background-color: #363342;
}

.section-description {
  color: #908caa;
  font-size: 0.9rem;
  line-height: 1.4;
}

.parameters-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-group label {
  color: #e0def4;
  font-size: 0.9rem;
  flex: 1;
}

.form-control {
  flex: 2;
  padding: 6px 12px;
  background-color: #2a273f;
  border: 1px solid #44415a;
  border-radius: 4px;
  color: #e0def4;
  font-size: 0.9rem;
  outline: none;
}

.ai-response-section {
  max-height: 300px;
  overflow-y: auto;
}

.ai-response-content {
  background-color: #2a273f;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.85rem;
  color: #e0def4;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
}

.processing-status,
.result-status,
.error-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #3e8fb0;
  border-top: 3px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.status-icon.success {
  background-color: #9ccfd8;
  color: #232136;
}

.status-icon.error {
  background-color: #eb6f92;
  color: #232136;
}

.status-message {
  display: flex;
  flex-direction: column;
}

.status-title {
  font-weight: 600;
  color: #e0def4;
}

.status-detail {
  font-size: 0.85rem;
  color: #908caa;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.footer-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  border: none;
}

.footer-button.cancel {
  background-color: transparent;
  border: 1px solid #44415a;
  color: #908caa;
}

.footer-button.primary {
  background-color: #3e8fb0;
  color: #232136;
}

.footer-button.primary:hover {
  background-color: #56a7c7;
}

.footer-button.cancel:hover {
  background-color: #2a273f;
}

.modal-title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.prompt-setting-btn {
  background: #3e8fb0;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 0.92rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  box-shadow: 0 2px 8px rgba(62,143,176,0.08);
  transition: background 0.2s;
}
.prompt-setting-btn.small {
  padding: 2px 8px;
  font-size: 0.85rem;
}
.prompt-setting-btn:hover {
  background: #56a7c7;
}

.ai-warning-bar {
  background: #eb6f92;
  color: #fff;
  font-weight: bold;
  text-align: center;
  padding: 10px 0;
  border-radius: 6px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(235,111,146,0.08);
  letter-spacing: 0.02em;
  font-size: 1rem;
}

/* 分批处理进度条样式 */
.batch-progress {
  margin-top: 12px;
  padding: 8px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 0.85em;
  color: #b8b5d6;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #44415a;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3e8fb0, #4ea8c6);
  border-radius: 3px;
  transition: width 0.3s ease;
}
</style>