<template>
  <div class="character-table-container">
    <table class="preset-table auto-width-table">
      <thead>
        <tr>
          <th class="column-type">类型</th>
          <th class="column-name">名称</th>
          <th class="column-description">视觉特征</th>
          <th v-if="showActions" class="column-actions">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="items.length === 0">
          <td :colspan="showActions ? 4 : 3" class="no-data-cell">暂无数据</td>
        </tr>
        <tr v-for="(item, index) in items" :key="item.id || item.name || index" class="preset-table-row">
          <td class="cell-type">[{{ item.type === 'character' ? '角色' : '特殊' }}]</td>
          <td class="cell-name">{{ item.name }}</td>
          <td class="cell-description">{{ item.description }}</td>
          <td v-if="showActions" class="cell-actions">
            <button @click="$emit('edit', item)" class="icon-button edit-preset-button" title="编辑">
              <svg viewBox="0 0 24 24" fill="currentColor" width="18" height="18"><path d="M12.962 2.19l7.846 7.846a2.25 2.25 0 010 3.182l-1.407 1.407-9.253-9.253 1.407-1.407a2.25 2.25 0 011.407-.675zm-1.407 1.407L3.948 11.204a2.252 2.252 0 00-.675 1.407V18.75c0 .414.336.75.75.75h6.139a2.252 2.252 0 001.407-.675L19.176 10.22l-9.028-9.028-1.591 1.591zM4.5 19.5V13.06l5.432 5.432H4.5zM12 19.5H6.568l5.432-5.432L12 14.068V19.5z" /></svg>
            </button>
            <button @click="$emit('delete', item)" class="icon-button delete-preset-button" title="删除">
              <svg viewBox="0 0 24 24" fill="currentColor" width="18" height="18"><path d="M7 4V2H17V4H22V6H20V21C20 22.1046 19.1046 23 18 23H6C4.89543 23 4 22.1046 4 21V6H2V4H7ZM6 6V21H18V6H6ZM9 9H11V19H9V9ZM13 9H15V19H13V9Z" /></svg>
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
defineProps({
  items: { type: Array, required: true, default: () => [] },
  showActions: { type: Boolean, default: true }
});
</script>

<style scoped>
.character-table-container { width: 100%; }
.preset-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
  background-color: #2a273f;
  color: #e0def4;
  table-layout: auto;
}
.preset-table th,
.preset-table td {
  border: 1px solid #44415a;
  padding: 10px 12px;
  text-align: left;
  vertical-align: middle;
}
.preset-table th {
  background-color: #232136;
  font-weight: 600;
  font-size: 0.9em;
  color: #b8b5d6;
}
.column-type { min-width: 60px; white-space: nowrap; }
.column-name { min-width: 90px; white-space: nowrap; }
.column-description { white-space: normal; }
.column-actions { min-width: 60px; text-align: center; white-space: nowrap; }
.cell-type, .cell-name { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.cell-description {
  color: #b8b5d6;
  line-height: 1.6;
  white-space: pre-line;
  word-break: break-word;
  font-size: 0.98em;
  max-width: 100%;
  overflow: hidden;
}
.cell-actions { text-align: right; }
.edit-preset-button, .delete-preset-button {
  background: none;
  border: none;
  padding: 0 4px;
  margin: 0 2px;
  cursor: pointer;
  color: #3e8fb0;
  transition: color 0.18s, background 0.18s;
  border-radius: 4px;
  font-size: 1em;
}
.edit-preset-button:hover, .delete-preset-button:hover {
  color: #4ea8c6;
  background: #232f3e;
}
.no-data-cell {
  text-align: center;
  color: #908caa;
  font-style: italic;
  background: #232136;
}
</style> 