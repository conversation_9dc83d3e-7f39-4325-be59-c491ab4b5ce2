{"globalReasoning": {"default": ["# AI绘画角色造型提取指令 (最终版)", "", "## 任务目标：", "1.  **首要任务：理解文本的时代背景与题材。** AI需首先尝试根据文本的用词、描述、情节元素、环境氛围等，判断故事发生的时代背景（如：古代中国、中世纪欧洲、现代都市、近未来、远未来星际等）和主要题材（如：武侠、仙侠、玄幻、奇幻、科幻、言情、悬疑等）。", "2.  **角色信息提取与造型推断：** 从提供的小说文本片段中，识别并提取主要角色的人物信息。根据文本描述及推断出的时代背景与题材，推断或总结其视觉造型特征。", "3.  **完整性与明确性要求：**", "    *   当文本信息不足时，AI必须基于上下文、推断的时代背景、常见题材设定或合理想象，为所有必需的造型栏目（性别、年龄、发型、服装）提供一个具体、可绘画的描述。", "    *   **输出的每一个造型特征（性别、年龄、发型、服装等）都必须是单一且明确的。严禁使用'或'、'可能'、'也许'、'例如'、'比如'等词语给出多个选项或不确定的描述。AI必须在多种可能性中做出唯一选择。**", "    *   **绝不允许使用'未知'、'未提及'、'不详'等表示信息缺失的词语。**", "", "## 文本背景感知与应用规则：", "*   **分析判断**：AI需在处理角色前，先对文本片段进行整体分析，明确或尽可能推断出其时代背景和题材。", "*   **造型约束**：在推断角色的发型、服装时，**必须严格遵守推断出的时代背景和题材的常见设定与视觉风格。**", "*   **避免时代错乱**：**核心原则：除非文本明确提及穿越、特殊设定或有强烈的反差暗示，否则严禁为非现代背景角色设计典型的现代服装，或为东方古典背景角色设计纯西方特有的古典服饰（反之亦然，除非有明确的文化融合设定）。**", "*   **若背景模糊**：如果文本片段极短，难以明确判断背景，AI应选择一个基于文本中有限线索最有可能的背景进行推断，并在此基础上设计造型。", "", "## 输出格式、语言及最终呈现要求：", "**最终输出应仅为角色列表，不包含任何其他文字、标题或解释。**", "每个角色信息占一行，格式如下：", "[角色名字 (原文)] - [Gender (English)], [Age/Age Range (English)], [<PERSON>style (English)], [<PERSON><PERSON><PERSON> (English)]", "", "*   **角色名字 (Character Name)**：保持文本中给出的原始语言（例如，如果原文是中文名，则输出中文名；如果原文是英文名，则输出英文名）。", "*   **所有描述词 (Gender, Age, Hairstyle, Clothing)**：**必须全部使用英文表述，确保单一明确。**", "    *   Gender: e.g., Male, Female", "    *   Age/Age Range: e.g., approx. 20 years old, young adult, late teens, elderly", "    *   Hairstyle: e.g., black long straight hair, golden shoulder-length wavy hair, dark brown high ponytail", "    *   Clothing: e.g., dark blue long robe, white hanfu with light pink outer robe, black tight-fitting combat suit", "", "## 提取与推断规则：", "1.  **角色识别**：识别文本中明确提及的角色名字。", "2.  **性别判断 (Gender - English)**：优先根据名字、称谓或描述判断。**若无法明确判断，必须基于名字的常见性别联想或上下文氛围进行合理推测并指定一个英文性别描述。**", "3.  **年龄推断 (Age - English)**：优先使用文本明确信息。**若无明确信息，必须根据角色行为、称呼、对话风格或与其他角色的关系，推断一个符合其定位的具体英文年龄描述。**", "4.  **发型与发色 (Hairstyle - English)**：优先提取文本明确描述。**若信息不完整或缺失，必须结合推断的时代背景、题材、角色性别、年龄及可能的身份/职业，设定一个具体、视觉化、单一明确且使用英文描述的发型与发色组合。**", "5.  **服装与颜色 (Clothing - English)**：优先提取文本明确描述。**若信息不完整或缺失，必须结合推断的时代背景、题材、角色性别、年龄、可能的身份/职业及所处环境，设定一套具体、符合逻辑、单一明确且使用英文描述的服装与颜色。**", "6.  **核心原则：提供完整且可绘画的细节**：所有输出的造型描述都应该是具体、完整且可以直接用于AI绘画提示的。", "7.  **专注视觉特征（再次强调）**：**仅关注**可直接用于绘画的静态视觉描述。**严格排除**非直接视觉造型元素。", "8.  **一致性与区分度**：如果同时提取多个角色，在推断信息时，尽量使不同角色的造型具有一定的区分度（除非是制服等）。", "9.  **简洁性**：在保证信息完整和准确的前提下，尽量使描述简洁明了。", "", "## 示例：", "**输入文本片段1 (玄幻/仙侠背景暗示，中文名):**", "\"紫云峰顶，一位白发老者凭虚御风，身旁跟随着两名弟子。左边的弟子名唤林轩，神情坚毅；右边的少女名为苏瑶，灵动可爱。\"", "", "白发老者 - Male, approx. 75 years old, snow-white long hair and beard styled into a Daoist bun, grey wide-sleeved Daoist robe", "林轩 - Male, approx. 20 years old, black long hair tied into a high ponytail with a cyan ribbon, cyan sect-uniform style long-sleeved shirt with fitted sleeves", "苏瑶 - Female, approx. 16 years old, jet-black long hair styled into double hanging buns adorned with a light-colored hairband, light pink cross-collared Hanfu dress", "", "**输入文本片段2 (近未来科幻背景暗示，英文代号):**", "\"Neon-lit streets hummed as 'Night Owl', a lone operative, moved through the shadows. His target: the cyborg 'Iron Fist', who was striding into an underground bar.\"", "", "Night Owl - Male, approx. 30 years old, black short choppy hair concealed by a dark grey hood, black high-tech material tactical trench coat over a dark tight-fitting undersuit", "Iron Fist - Male, approx. 35 years old (appearance might be altered by cybernetics), silver-grey short crew-cut hair with a visible metallic implant on the right temple, upper body largely exposed showing dark cyan mechanical prosthetics and metallic endoskeleton, wearing worn-out dark khaki cargo pants", "", "---", "", "请根据以上规则，处理我接下来提供的文本。务必先分析文本的时代背景和题材，并在此基础上进行角色的造型推断与补全。确保角色名字使用原文，所有造型描述（性别、年龄、发型、服装）都使用英文，并且是具体、可绘画、单一明确且符合背景的描述。", "**最终输出时，只给出角色列表，每行一个角色及其描述，不添加任何额外文字或解释。**", "请根据以上规则，处理我接下来提供的文本：\n[TEXT_PLACEHOLDER]"], "user": ["# AI绘画角色造型提取指令 (最终版)", "", "## 任务目标：", "1.  **首要任务：理解文本的时代背景与题材。** AI需首先尝试根据文本的用词、描述、情节元素、环境氛围等，判断故事发生的时代背景（如：古代中国、中世纪欧洲、现代都市、近未来、远未来星际等）和主要题材（如：武侠、仙侠、玄幻、奇幻、科幻、言情、悬疑等）。", "2.  **角色信息提取与造型推断：** 从提供的小说文本片段中，识别并提取主要角色的人物信息。根据文本描述及推断出的时代背景与题材，推断或总结其视觉造型特征。", "3.  **完整性与明确性要求：**", "    *   当文本信息不足时，AI必须基于上下文、推断的时代背景、常见题材设定或合理想象，为所有必需的造型栏目（性别、年龄、发型、服装）提供一个具体、可绘画的描述。", "    *   **输出的每一个造型特征（性别、年龄、发型、服装等）都必须是单一且明确的。严禁使用'或'、'可能'、'也许'、'例如'、'比如'等词语给出多个选项或不确定的描述。AI必须在多种可能性中做出唯一选择。**", "    *   **绝不允许使用'未知'、'未提及'、'不详'等表示信息缺失的词语。**", "", "## 文本背景感知与应用规则：", "*   **分析判断**：AI需在处理角色前，先对文本片段进行整体分析，明确或尽可能推断出其时代背景和题材。", "*   **造型约束**：在推断角色的发型、服装时，**必须严格遵守推断出的时代背景和题材的常见设定与视觉风格。**", "*   **避免时代错乱**：**核心原则：除非文本明确提及穿越、特殊设定或有强烈的反差暗示，否则严禁为非现代背景角色设计典型的现代服装，或为东方古典背景角色设计纯西方特有的古典服饰（反之亦然，除非有明确的文化融合设定）。**", "*   **若背景模糊**：如果文本片段极短，难以明确判断背景，AI应选择一个基于文本中有限线索最有可能的背景进行推断，并在此基础上设计造型。", "", "## 输出格式、语言及最终呈现要求：", "**最终输出应仅为角色列表，不包含任何其他文字、标题或解释。**", "每个角色信息占一行，格式如下：", "[角色名字 (原文)] - [Gender (English)], [Age/Age Range (English)], [<PERSON>style (English)], [<PERSON><PERSON><PERSON> (English)]", "", "*   **角色名字 (Character Name)**：保持文本中给出的原始语言（例如，如果原文是中文名，则输出中文名；如果原文是英文名，则输出英文名）。", "*   **所有描述词 (Gender, Age, Hairstyle, Clothing)**：**必须全部使用英文表述，确保单一明确。**", "    *   Gender: e.g., Male, Female", "    *   Age/Age Range: e.g., approx. 20 years old, young adult, late teens, elderly", "    *   Hairstyle: e.g., black long straight hair, golden shoulder-length wavy hair, dark brown high ponytail", "    *   Clothing: e.g., dark blue long robe, white hanfu with light pink outer robe, black tight-fitting combat suit", "", "## 提取与推断规则：", "1.  **角色识别**：识别文本中明确提及的角色名字。", "2.  **性别判断 (Gender - English)**：优先根据名字、称谓或描述判断。**若无法明确判断，必须基于名字的常见性别联想或上下文氛围进行合理推测并指定一个英文性别描述。**", "3.  **年龄推断 (Age - English)**：优先使用文本明确信息。**若无明确信息，必须根据角色行为、称呼、对话风格或与其他角色的关系，推断一个符合其定位的具体英文年龄描述。**", "4.  **发型与发色 (Hairstyle - English)**：优先提取文本明确描述。**若信息不完整或缺失，必须结合推断的时代背景、题材、角色性别、年龄及可能的身份/职业，设定一个具体、视觉化、单一明确且使用英文描述的发型与发色组合。**", "5.  **服装与颜色 (Clothing - English)**：优先提取文本明确描述。**若信息不完整或缺失，必须结合推断的时代背景、题材、角色性别、年龄、可能的身份/职业及所处环境，设定一套具体、符合逻辑、单一明确且使用英文描述的服装与颜色。**", "6.  **核心原则：提供完整且可绘画的细节**：所有输出的造型描述都应该是具体、完整且可以直接用于AI绘画提示的。", "7.  **专注视觉特征（再次强调）**：**仅关注**可直接用于绘画的静态视觉描述。**严格排除**非直接视觉造型元素。", "8.  **一致性与区分度**：如果同时提取多个角色，在推断信息时，尽量使不同角色的造型具有一定的区分度（除非是制服等）。", "9.  **简洁性**：在保证信息完整和准确的前提下，尽量使描述简洁明了。", "", "## 示例：", "**输入文本片段1 (玄幻/仙侠背景暗示，中文名):**", "\"紫云峰顶，一位白发老者凭虚御风，身旁跟随着两名弟子。左边的弟子名唤林轩，神情坚毅；右边的少女名为苏瑶，灵动可爱。\"", "", "白发老者 - Male, approx. 75 years old, snow-white long hair and beard styled into a Daoist bun, grey wide-sleeved Daoist robe", "林轩 - Male, approx. 20 years old, black long hair tied into a high ponytail with a cyan ribbon, cyan sect-uniform style long-sleeved shirt with fitted sleeves", "苏瑶 - Female, approx. 16 years old, jet-black long hair styled into double hanging buns adorned with a light-colored hairband, light pink cross-collared Hanfu dress", "", "**输入文本片段2 (近未来科幻背景暗示，英文代号):**", "\"Neon-lit streets hummed as 'Night Owl', a lone operative, moved through the shadows. His target: the cyborg 'Iron Fist', who was striding into an underground bar.\"", "", "Night Owl - Male, approx. 30 years old, black short choppy hair concealed by a dark grey hood, black high-tech material tactical trench coat over a dark tight-fitting undersuit", "Iron Fist - Male, approx. 35 years old (appearance might be altered by cybernetics), silver-grey short crew-cut hair with a visible metallic implant on the right temple, upper body largely exposed showing dark cyan mechanical prosthetics and metallic endoskeleton, wearing worn-out dark khaki cargo pants", "", "---", "", "请根据以上规则，处理我接下来提供的文本。务必先分析文本的时代背景和题材，并在此基础上进行角色的造型推断与补全。确保角色名字使用原文，所有造型描述（性别、年龄、发型、服装）都使用英文，并且是具体、可绘画、单一明确且符合背景的描述。", "**最终输出时，只给出角色列表，每行一个角色及其描述，不添加任何额外文字或解释。**", "请根据以上规则，处理我接下来提供的文本：", "[TEXT_PLACEHOLDER]"]}, "aiGrouping": {"default": ["AI助手指令：文本至智能分镜转换器", "", "任务目标:", "你的任务是将用户提供的叙事文本（每行以数字序号开头）转换成一系列独立的、以新数字序号开头的视觉分镜文本行。核心目标是智能地分割与合并原始文本，使得每一个输出行都代表一个连贯的视觉画面、一个自然的叙事节奏点，或者是屏幕上独立展示的标题/关键文本。在处理过程中，优先确保叙事的流畅性和自然的过渡感，同时避免单行信息过于密集导致焦点模糊，也要防止过度分割使得节奏支离破碎。最终输出应非常适合直接用于后续的视觉化创作（如漫画、动画脚本）。", "", "工作流程:", "", "1.  文本处理与分镜生成:", "    *   输入解析: 对于用户提供的每一行文本，你需要忽略行首的数字序号及其后的任何标点或空格 (例如，忽略 \"1. \" 或 \"10. \" )，只提取并处理该序号之后的实际文本内容。", "    *   核心逻辑执行: 根据下方\"智能分镜生成规则\"处理提取出的文本内容，进行智能分割与合并。", "    *   输出: 按照\"输出格式规范\"严格输出处理后的分镜文本行。", "", "智能分镜生成规则:", "", "*   基本原则：视觉连贯与叙事节奏", "    *   每一个输出的分镜行，理想情况下应该捕捉一个相对独立的视觉瞬间、一个动作的阶段、一个关键的人物表情或反应，或者一个清晰的叙事信息单元。", "    *   独立文本元素：如果一行原始内容是章节标题（如\"第十章\"）、关键概念首次作为标题性文字出现（如\"真阳秘法\"），它本身应构成一个独立的视觉呈现焦点，通常单独成行，不与前后叙事内容合并（除非它是紧随动作的直接宾语，见合并条件）。", "", "*   分割决策（何时创建新分镜行）：", "    *   新焦点/阶段： 当新的文本内容引入了显著不同的视觉焦点、开启了新的叙事阶段、或传递了新的核心信息时。", "    *   动作/状态转变： 当描述的核心动作或角色状态发生明显改变或告一段落。", "    *   主体切换： 当视觉叙事的主体（人物、物体）发生明确转换。", "    *   时空变化： 当场景、时间发生明确跳转。", "    *   对话开始/转换： 当对话开始，或对话的说话人发生改变。", "    *   强转折： 当遇到表示强烈转折的连词时（如\"但是\"、\"然而\"、\"因此\"、\"于是\"、\"所以\"、\"紧接着\"、\"更糟的是\"），通常这些词会开启一个新的叙事节拍，应将该连词置于新分镜行的开头。", "    *   避免冗杂： 如果将后续文本合并到当前行会导致信息过载、包含多个不相关的视觉焦点，或严重影响阅读的自然节奏，则必须分割。", "", "*   合并决策（何时将后续文本融入当前分镜行 - 优先流畅）：", "    *   前提条件： 只有当合并后的整行文本仍然服务于一个单一且连贯的视觉单元或紧密的叙事流，并且合并后行文自然、不过于冗长或复杂时，才考虑合并。注意：独立的标题/概念行通常不参与合并，除非作为动作的直接对象。", "    *   倾向合并以保流畅：", "        *   直接延续/补充： 后续文本是对当前视觉焦点或叙事节拍的直接延伸、补充说明、紧密相连的微小动作/状态，或直接产生的结果。此时用英文逗号 (\",\") 连接。", "        *   连续微动作： 如\"他点了点头,嗯了一声\"。", "        *   描述+伴随状态： 如\"他脸色苍白,微微颤抖\"。", "        *   简短引语+说话人： 如\"他说,'我们走'\"。", "        *   时间/条件状语+主事件： 如\"第二天早上,他发现了一封信\"。这类情况优先合并以保持叙事连贯，除非合并后显著增加复杂性。", "        *   动作+直接短宾语： 如\"他举起了,那把剑\"。", "        *   非强转折连词： 对于\"并且\"、\"而且\"等连词，如果其连接的内容与前文紧密相关且属于同一视觉/叙事单元，可以合并以增强流畅感。若其后内容开启新焦点，则按分割处理。", "", "*   核心平衡：流畅优先，兼顾清晰", "    *   在确保每个分镜行具有相对清晰的视觉或叙事意义的前提下，首要目标是通过合理的合并来维持叙事的自然流畅度和节奏感。灵活判断，避免教条式的分割。仅当合并会显著损害清晰度、引入过多焦点或造成阅读困难时才进行分割。语感和上下文是重要参考。", "", "*   文本保真性：", "    *   在分割和合并过程中，必须使用原始文本片段，不得进行任何形式的改写、概括或删减。", "", "*   避免过度碎片化：", "    *   主动避免创建那些只包含单个连词（如\"但\"单独一行）或极短的、无独立意义的连接性短语的行。每个分镜行都应包含一个有意义的动作、描述、对话片段或叙事推进。", "", "输出格式规范:", "", "1.  添加新序号： 每一个最终生成的分镜文本行，都应以新的、从1开始的连续数字序号开头，格式为 '数字.' (例如 '1. ', '2. ', '10. ')", "2.  单独占行： 每一个分镜文本行（包括其新序号）单独占据一行。", "3.  内容格式： 新序号之后紧跟该分镜对应的（可能经过合并的）原始文本内容。", "4.  合并连接符： 如果一行内的文本内容包含多个原始文本片段，它们之间用英文逗号 (\",\") 进行连接。", "5.  无末尾逗号： 行的文本内容末尾不应有多余的逗号。", "6.  禁止原始序号： 绝对禁止在输出的分镜文本行内容中包含原始文本中的数字序号或任何时间戳信息。", "7.  纯净输出： 最终输出仅为处理后的、带新序号的分镜文本行序列，不含任何其他解释、标题或元信息。", "", "下面是需要处理的原始叙事文本（每行以数字序号开头）：", "---", "请处理以下原始文本：", "{textContent}", "---", "请严格按照上述规则处理并输出分镜文本。"], "user": ["AI助手指令：文本至智能分镜转换器", "", "任务目标:", "你的任务是将用户提供的叙事文本（每行以数字序号开头）转换成一系列独立的、以新数字序号开头的视觉分镜文本行。核心目标是智能地分割与合并原始文本，使得每一个输出行都代表一个连贯的视觉画面、一个自然的叙事节奏点，或者是屏幕上独立展示的标题/关键文本。在处理过程中，优先确保叙事的流畅性和自然的过渡感，同时避免单行信息过于密集导致焦点模糊，也要防止过度分割使得节奏支离破碎。最终输出应非常适合直接用于后续的视觉化创作（如漫画、动画脚本）。", "", "工作流程:", "", "1.  文本处理与分镜生成:", "    *   输入解析: 对于用户提供的每一行文本，你需要忽略行首的数字序号及其后的任何标点或空格 (例如，忽略 \"1. \" 或 \"10. \" )，只提取并处理该序号之后的实际文本内容。", "    *   核心逻辑执行: 根据下方\"智能分镜生成规则\"处理提取出的文本内容，进行智能分割与合并。", "    *   输出: 按照\"输出格式规范\"严格输出处理后的分镜文本行。", "", "智能分镜生成规则:", "", "*   基本原则：视觉连贯与叙事节奏", "    *   每一个输出的分镜行，理想情况下应该捕捉一个相对独立的视觉瞬间、一个动作的阶段、一个关键的人物表情或反应，或者一个清晰的叙事信息单元。", "    *   独立文本元素：如果一行原始内容是章节标题（如\"第十章\"）、关键概念首次作为标题性文字出现（如\"真阳秘法\"），它本身应构成一个独立的视觉呈现焦点，通常单独成行，不与前后叙事内容合并（除非它是紧随动作的直接宾语，见合并条件）。", "", "*   分割决策（何时创建新分镜行）：", "    *   新焦点/阶段： 当新的文本内容引入了显著不同的视觉焦点、开启了新的叙事阶段、或传递了新的核心信息时。", "    *   动作/状态转变： 当描述的核心动作或角色状态发生明显改变或告一段落。", "    *   主体切换： 当视觉叙事的主体（人物、物体）发生明确转换。", "    *   时空变化： 当场景、时间发生明确跳转。", "    *   对话开始/转换： 当对话开始，或对话的说话人发生改变。", "    *   强转折： 当遇到表示强烈转折的连词时（如\"但是\"、\"然而\"、\"因此\"、\"于是\"、\"所以\"、\"紧接着\"、\"更糟的是\"），通常这些词会开启一个新的叙事节拍，应将该连词置于新分镜行的开头。", "    *   避免冗杂： 如果将后续文本合并到当前行会导致信息过载、包含多个不相关的视觉焦点，或严重影响阅读的自然节奏，则必须分割。", "", "*   合并决策（何时将后续文本融入当前分镜行 - 优先流畅）：", "    *   前提条件： 只有当合并后的整行文本仍然服务于一个单一且连贯的视觉单元或紧密的叙事流，并且合并后行文自然、不过于冗长或复杂时，才考虑合并。注意：独立的标题/概念行通常不参与合并，除非作为动作的直接对象。", "    *   倾向合并以保流畅：", "        *   直接延续/补充： 后续文本是对当前视觉焦点或叙事节拍的直接延伸、补充说明、紧密相连的微小动作/状态，或直接产生的结果。此时用英文逗号 (\",\") 连接。", "        *   连续微动作： 如\"他点了点头,嗯了一声\"。", "        *   描述+伴随状态： 如\"他脸色苍白,微微颤抖\"。", "        *   简短引语+说话人： 如\"他说,'我们走'\"。", "        *   时间/条件状语+主事件： 如\"第二天早上,他发现了一封信\"。这类情况优先合并以保持叙事连贯，除非合并后显著增加复杂性。", "        *   动作+直接短宾语： 如\"他举起了,那把剑\"。", "        *   非强转折连词： 对于\"并且\"、\"而且\"等连词，如果其连接的内容与前文紧密相关且属于同一视觉/叙事单元，可以合并以增强流畅感。若其后内容开启新焦点，则按分割处理。", "", "*   核心平衡：流畅优先，兼顾清晰", "    *   在确保每个分镜行具有相对清晰的视觉或叙事意义的前提下，首要目标是通过合理的合并来维持叙事的自然流畅度和节奏感。灵活判断，避免教条式的分割。仅当合并会显著损害清晰度、引入过多焦点或造成阅读困难时才进行分割。语感和上下文是重要参考。", "", "*   文本保真性：", "    *   在分割和合并过程中，必须使用原始文本片段，不得进行任何形式的改写、概括或删减。", "", "*   避免过度碎片化：", "    *   主动避免创建那些只包含单个连词（如\"但\"单独一行）或极短的、无独立意义的连接性短语的行。每个分镜行都应包含一个有意义的动作、描述、对话片段或叙事推进。", "", "输出格式规范:", "", "1.  添加新序号： 每一个最终生成的分镜文本行，都应以新的、从1开始的连续数字序号开头，格式为 '数字.' (例如 '1. ', '2. ', '10. ')", "2.  单独占行： 每一个分镜文本行（包括其新序号）单独占据一行。", "3.  内容格式： 新序号之后紧跟该分镜对应的（可能经过合并的）原始文本内容。", "4.  合并连接符： 如果一行内的文本内容包含多个原始文本片段，它们之间用英文逗号 (\",\") 进行连接。", "5.  无末尾逗号： 行的文本内容末尾不应有多余的逗号。", "6.  禁止原始序号： 绝对禁止在输出的分镜文本行内容中包含原始文本中的数字序号或任何时间戳信息。", "7.  纯净输出： 最终输出仅为处理后的、带新序号的分镜文本行序列，不含任何其他解释、标题或元信息。", "", "下面是需要处理的原始叙事文本（每行以数字序号开头）：", "---", "请处理以下原始文本：", "{textContent}", "---", "请严格按照上述规则处理并输出分镜文本。"]}, "specialNaming": {"default": ["# 智能特名与视觉化提示词生成指令", "", "你现在是一个资深的小说概念艺术家助手。请仔细阅读以下小说文本。", "你的任务是：", "1.  识别并提取文本中描述的特别物品、道具、功法（或其视觉表现）、以及具有奇幻或非现实色彩的独特场景。", "2.  为每一个提取出的元素，根据文本描述的原始名称（如果文本未明确给出，则根据描述创造一个贴切的中文名称），并生成一句到两句的英文视觉化提示词（prompt）。", "3.  生成的英文提示词必须严格依据文本提供的时代背景、文化特征和具体描述。", "    *   对于物品或场景，描述其外观和氛围。例如：", "        *   若文本描述\"一枚散发幽光的丹药\"，英文提示词应类似 'A softly glowing, spherical elixir, possibly with subtle arcane patterns, radiating a mystical aura'，而不是 'A medical pill'。", "        *   若文本描述\"一座古代中国的破旧茅屋\"，英文提示词应类似 'A dilapidated ancient Chinese thatched cottage with weathered wooden beams, a sagging roof, and paper-patched windows, nestled in a misty landscape'，而不是 'An old rundown house'。", "        *   若文本描述\"悬浮在云海中的仙山\"，视觉化提示词可以描述 'A majestic, ethereal mountain peak floating amidst a sea of swirling clouds, with fantastical architecture, glowing flora, and perhaps mythical creatures soaring around it.'", "    *   **对于功法、剑法或技能类的描述，请专注于其视觉特效本身，不要包含施法者或人物动作。例如：**", "        *   若文本描述一种\"万剑归宗\"的剑法特效，视觉化提示词应描述其特效，例如 'Myriad ethereal swords of pure energy materializing from the void, converging into a blinding torrent of sharp light, radiating immense power.' 或 'A storm of glowing, phantom blades, swirling and cutting through the air with incredible speed, leaving trails of brilliant light.'", "        *   若文本描述一种\"九阳神功\"的内力特效，视觉化提示词可以描述 'Intense, golden-red energy radiating outwards like a miniature sun, with visible heat distortion and crackling arcs of pure power.'", "4.  输出格式严格为：", "    [中文名称] - [英文视觉化提示词]", "    每个条目占一行。", "", "请只输出提取和转换后的结果列表，不要包含任何解释、标题或额外的对话。", "", "以下是小说文本：", "[TEXT_PLACEHOLDER]"], "user": ["# 智能特名与视觉化提示词生成指令", "", "你现在是一个资深的小说概念艺术家助手。请仔细阅读以下小说文本。", "你的任务是：", "1.  识别并提取文本中描述的特别物品、道具、功法（或其视觉表现）、以及具有奇幻或非现实色彩的独特场景。", "2.  为每一个提取出的元素，根据文本描述的原始名称（如果文本未明确给出，则根据描述创造一个贴切的中文名称），并生成一句到两句的英文视觉化提示词（prompt）。", "3.  生成的英文提示词必须严格依据文本提供的时代背景、文化特征和具体描述。", "    *   对于物品或场景，描述其外观和氛围。例如：", "        *   若文本描述\"一枚散发幽光的丹药\"，英文提示词应类似 'A softly glowing, spherical elixir, possibly with subtle arcane patterns, radiating a mystical aura'，而不是 'A medical pill'。", "        *   若文本描述\"一座古代中国的破旧茅屋\"，英文提示词应类似 'A dilapidated ancient Chinese thatched cottage with weathered wooden beams, a sagging roof, and paper-patched windows, nestled in a misty landscape'，而不是 'An old rundown house'。", "        *   若文本描述\"悬浮在云海中的仙山\"，视觉化提示词可以描述 'A majestic, ethereal mountain peak floating amidst a sea of swirling clouds, with fantastical architecture, glowing flora, and perhaps mythical creatures soaring around it.'", "    *   **对于功法、剑法或技能类的描述，请专注于其视觉特效本身，不要包含施法者或人物动作。例如：**", "        *   若文本描述一种\"万剑归宗\"的剑法特效，视觉化提示词应描述其特效，例如 'Myriad ethereal swords of pure energy materializing from the void, converging into a blinding torrent of sharp light, radiating immense power.' 或 'A storm of glowing, phantom blades, swirling and cutting through the air with incredible speed, leaving trails of brilliant light.'", "        *   若文本描述一种\"九阳神功\"的内力特效，视觉化提示词可以描述 'Intense, golden-red energy radiating outwards like a miniature sun, with visible heat distortion and crackling arcs of pure power.'", "4.  输出格式严格为：", "    [中文名称] - [英文视觉化提示词]", "    每个条目占一行。", "", "请只输出提取和转换后的结果列表，不要包含任何解释、标题或额外的对话。", "", "以下是小说文本：", "[TEXT_PLACEHOLDER]"]}, "smartScene": {"default": ["# 智能场景分析专家指令（确定性与视觉化英文时间提示版）", "", "你是一位富有高度想象力、能做出果断判断、深度理解小说世界观的场景分析专家。你的任务是细致分析你将收到的原始文本（每条代表小说中的一个段落），提取其中明确指出或间接暗示的场景（以单一、确定的纯视觉化描述呈现）和相关的时间信息（以单一、确定的、能唤起视觉联想的英文描述呈现）。", "", "请严格遵循以下要求：", "", "场景识别与纯视觉化呈现 (Scene Identification & Purely Visual Representation) - 绝对确定性：", "", "根据文本内容，自动识别出文本段落中的核心场景信息。", "", "核心要求： 你的目标是为每个场景生成一个单一、确定的、极其简短（通常1-3个短语或约5-10字）且仅包含纯粹视觉元素的描述性短语或词组组合。这个描述应直接构建出场景的画面，并直接适用于AI绘画的提示。", "", "严禁不确定性或选择性描述： 在场景描述中，绝对不允许使用任何表示不确定、推测、选择或模糊的词语（例如：\"或\"、\"或者\"、\"可能\"、\"大概\"、\"类似\"、\"似乎\"、\"某种\"、\"待定\"、\"不明\"等）。你必须给出一个具体、肯定的描述。 如果文本细节不足，你需要基于已有的零散线索和对题材的理解，进行果断的创造性补全，形成一个明确的视觉画面。", "", "例如，如果文本只提到\"一个阴暗的角落\"，你不能说\"阴暗角落，可能堆着杂物\"，而应该基于小说背景断言，如（奇幻背景下）：\"蛛网遍布的石阶角落\"或（现代背景下）：\"废弃纸箱堆积的暗角\"。", "", "避免直接命名，侧重单一、确定的纯视觉描述： 同样，不使用简单名称或分类标签。输出必须是对场景样貌、布局、光影或关键视觉特征的唯一、确定的描绘。严格排除声音、气味、温度等非视觉感官信息。", "", "（场景描述的风格示例保持视觉化，但AI在应用时需确保其最终输出的确定性）", "", "视觉元素提取与融合： 从文本中提取构成场景的关键视觉元素，并将其巧妙地融合到你最终确定的描述词中，确保描述与文本的整体风格和题材背景高度一致。", "", "特殊/虚构地点处理： 对于特殊或虚构地点，同样运用单一、确定的、简短的纯视觉化描述词来呈现。", "", "场景连贯性： 当主要环境发生显著变化，或时间发生大的跳跃导致环境感知变化时，应视为进入新的场景。", "", "时间提示 (Time Hint - 单一、确定的视觉化英文描述，禁止\"未知\"，果断推断)：", "", "提取或推断每个场景发生的时间。", "", "核心要求：必须为每个场景提供一个单一、确定的、能唤起视觉联想的英文时间描述。严禁使用任何表示\"未知\"或不确定的词汇。如果文本没有提供明确的时间线索，你必须基于场景的视觉特征、光照条件、可能的自然现象或环境氛围，进行创造性的、果断的视觉化时间断言。", "", "严禁不确定性或选择性描述： 与场景描述一样，时间提示中也绝对不允许出现任何表示不确定或选择的词语。你必须给出一个明确的时间点或时间段的视觉化呈现。", "", "视觉化时间断言示例：", "", "文本暗示安静室内，无明确时间：不是 'Perhaps Sunbeams Through Window Gap'，而是 'Sunbeams Slant Through Window' (阳光斜射入窗) 或 'Shadows Stretch Long Indoors' (室内阴影拉长)。", "文本暗示飞船内部，无明确时间：不是 'Likely Star Streaks Past Viewport'，而是 'Distant Stars Glide Past Viewport' (远星划过舷窗) 或 'Unwavering Artificial Ceiling Light' (稳定的人造顶灯光芒)。", "", "所有时间提示都必须是英文且保持简洁，并尽可能地视觉化和确定化。", "", "标准时间提示示例（需确定化）：'Golden Dawn Light Filters In' (金色晨曦滤入), 'Direct Midday Sun Overhead' (正午阳光直射头顶), 'Deep Blue Twilight Descends' (深蓝暮色降临)。", "", "区间划分 (Line Range)：", "", "输出结果时，请将连续属于同一场景的段落合并为一个条目，并标明其在原始文本中的段落编号范围。", "", "输出格式 (Output Format)：", "", "请将所有分析结果以纯文本形式逐条列出。每个场景的信息应包含：", "", "Line Range:", "Location:", "Time:", "", "不同场景条目之间请使用 --- (三个连字符) 作为清晰的分隔符。", "", "最终输出只应包含此纯文本结果，不要有任何额外的解释、标题、引言或代码块标记。", "", "输出示例如下（请注意场景和时间描述的绝对确定性和视觉化）：", "", "Line Range: 1-5", "Location: Blue stone floor, single stone bed, early light streaks", "Time: First Light Piercing Darkness", "", "Line Range: 6-10", "Location: Polished metal corridor, flickering console lights", "Time: Deep Space, Starlight Window", "", "Line Range: 11-15", "Location: Jagged cave entrance, blood moon illuminates sharp rocks", "Time: Blood Moon at Zenith", "", "Line Range: 16-18", "Location: Sunlit forest clearing, massive gnarled oak stands", "Time: <PERSON>, Sun Directly Above", "", "(后续条目按照类似格式输出，覆盖所有提供的文本段落)", "", "请基于以上要求，依次遍历你收到的全部文本段落，生成并归纳每个区间的场景信息。如果某一区间中存在场景变动，请尽量细分区间。你必须为每个场景提供单一、确定的、极致简短且纯粹视觉化的描述。所有时间提示均需以单一、确定的、能唤起视觉联想的英文描述呈现，并在缺乏明确线索时进行果断的创造性断言。 请在生成结果时只输出纯文本数据。", "", "请处理以下原始文本：", "{textContent}"], "user": ["# 智能场景分析专家指令（确定性与视觉化英文时间提示版）", "", "你是一位富有高度想象力、能做出果断判断、深度理解小说世界观的场景分析专家。你的任务是细致分析你将收到的原始文本（每条代表小说中的一个段落），提取其中明确指出或间接暗示的场景（以单一、确定的纯视觉化描述呈现）和相关的时间信息（以单一、确定的、能唤起视觉联想的英文描述呈现）。", "", "请严格遵循以下要求：", "", "场景识别与纯视觉化呈现 (Scene Identification & Purely Visual Representation) - 绝对确定性：", "", "根据文本内容，自动识别出文本段落中的核心场景信息。", "", "核心要求： 你的目标是为每个场景生成一个单一、确定的、极其简短（通常1-3个短语或约5-10字）且仅包含纯粹视觉元素的描述性短语或词组组合。这个描述应直接构建出场景的画面，并直接适用于AI绘画的提示。", "", "严禁不确定性或选择性描述： 在场景描述中，绝对不允许使用任何表示不确定、推测、选择或模糊的词语（例如：\"或\"、\"或者\"、\"可能\"、\"大概\"、\"类似\"、\"似乎\"、\"某种\"、\"待定\"、\"不明\"等）。你必须给出一个具体、肯定的描述。 如果文本细节不足，你需要基于已有的零散线索和对题材的理解，进行果断的创造性补全，形成一个明确的视觉画面。", "", "例如，如果文本只提到\"一个阴暗的角落\"，你不能说\"阴暗角落，可能堆着杂物\"，而应该基于小说背景断言，如（奇幻背景下）：\"蛛网遍布的石阶角落\"或（现代背景下）：\"废弃纸箱堆积的暗角\"。", "", "避免直接命名，侧重单一、确定的纯视觉描述： 同样，不使用简单名称或分类标签。输出必须是对场景样貌、布局、光影或关键视觉特征的唯一、确定的描绘。严格排除声音、气味、温度等非视觉感官信息。", "", "（场景描述的风格示例保持视觉化，但AI在应用时需确保其最终输出的确定性）", "", "视觉元素提取与融合： 从文本中提取构成场景的关键视觉元素，并将其巧妙地融合到你最终确定的描述词中，确保描述与文本的整体风格和题材背景高度一致。", "", "特殊/虚构地点处理： 对于特殊或虚构地点，同样运用单一、确定的、简短的纯视觉化描述词来呈现。", "", "场景连贯性： 当主要环境发生显著变化，或时间发生大的跳跃导致环境感知变化时，应视为进入新的场景。", "", "时间提示 (Time Hint - 单一、确定的视觉化英文描述，禁止\"未知\"，果断推断)：", "", "提取或推断每个场景发生的时间。", "", "核心要求：必须为每个场景提供一个单一、确定的、能唤起视觉联想的英文时间描述。严禁使用任何表示\"未知\"或不确定的词汇。如果文本没有提供明确的时间线索，你必须基于场景的视觉特征、光照条件、可能的自然现象或环境氛围，进行创造性的、果断的视觉化时间断言。", "", "严禁不确定性或选择性描述： 与场景描述一样，时间提示中也绝对不允许出现任何表示不确定或选择的词语。你必须给出一个明确的时间点或时间段的视觉化呈现。", "", "视觉化时间断言示例：", "", "文本暗示安静室内，无明确时间：不是 'Perhaps Sunbeams Through Window Gap'，而是 'Sunbeams Slant Through Window' (阳光斜射入窗) 或 'Shadows Stretch Long Indoors' (室内阴影拉长)。", "文本暗示飞船内部，无明确时间：不是 'Likely Star Streaks Past Viewport'，而是 'Distant Stars Glide Past Viewport' (远星划过舷窗) 或 'Unwavering Artificial Ceiling Light' (稳定的人造顶灯光芒)。", "", "所有时间提示都必须是英文且保持简洁，并尽可能地视觉化和确定化。", "", "标准时间提示示例（需确定化）：'Golden Dawn Light Filters In' (金色晨曦滤入), 'Direct Midday Sun Overhead' (正午阳光直射头顶), 'Deep Blue Twilight Descends' (深蓝暮色降临)。", "", "区间划分 (Line Range)：", "", "输出结果时，请将连续属于同一场景的段落合并为一个条目，并标明其在原始文本中的段落编号范围。", "", "输出格式 (Output Format)：", "", "请将所有分析结果以纯文本形式逐条列出。每个场景的信息应包含：", "", "Line Range:", "Location:", "Time:", "", "不同场景条目之间请使用 --- (三个连字符) 作为清晰的分隔符。", "", "最终输出只应包含此纯文本结果，不要有任何额外的解释、标题、引言或代码块标记。", "", "输出示例如下（请注意场景和时间描述的绝对确定性和视觉化）：", "", "Line Range: 1-5", "Location: Blue stone floor, single stone bed, early light streaks", "Time: First Light Piercing Darkness", "", "Line Range: 6-10", "Location: Polished metal corridor, flickering console lights", "Time: Deep Space, Starlight Window", "", "Line Range: 11-15", "Location: Jagged cave entrance, blood moon illuminates sharp rocks", "Time: Blood Moon at Zenith", "", "Line Range: 16-18", "Location: Sunlit forest clearing, massive gnarled oak stands", "Time: <PERSON>, Sun Directly Above", "", "(后续条目按照类似格式输出，覆盖所有提供的文本段落)", "", "请基于以上要求，依次遍历你收到的全部文本段落，生成并归纳每个区间的场景信息。如果某一区间中存在场景变动，请尽量细分区间。你必须为每个场景提供单一、确定的、极致简短且纯粹视觉化的描述。所有时间提示均需以单一、确定的、能唤起视觉联想的英文描述呈现，并在缺乏明确线索时进行果断的创造性断言。 请在生成结果时只输出纯文本数据。", "", "请处理以下原始文本：", "{textContent}"]}}