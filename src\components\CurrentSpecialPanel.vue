<template>
  <div class="panel special-panel">
    <h4 class="panel-title">当前特名 ({{ items.length }})</h4>
    <CharacterTable
      :items="items"
      :show-actions="true"
      @edit="onEdit"
      @delete="onDelete"
    />
  </div>
</template>

<script setup>
import CharacterTable from './CharacterTable.vue';
defineProps({
  items: { type: Array, required: true, default: () => [] },
  onEdit: { type: Function, required: true },
  onDelete: { type: Function, required: true }
});
</script>

<style scoped>
.panel.special-panel {
  background: #232136;
  border-radius: 6px;
  padding: 0;
  margin-top: 0;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.panel-title {
  font-size: 1.15em;
  color: #e0def4;
  font-weight: 600;
  margin: 0;
  padding: 15px 20px;
  border-bottom: 1px solid #44415a;
  flex-shrink: 0;
}
</style> 