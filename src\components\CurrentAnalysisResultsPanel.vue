<template>
  <div class="current-analysis-results-panel">
    <div
      v-if="!isLoading && (analyzedItems.characters.length > 0 || analyzedItems.eraBackground || rawOutput)"
      class="results-section"
    >
      <!-- 时代背景显示 -->
      <div
        v-if="analyzedItems.eraBackground"
        class="era-background-text"
      >
        <strong>时代背景:</strong> {{ analyzedItems.eraBackground }}
      </div>

      <!-- 分析结果表格 -->
      <div class="results-table-container">
        <CharacterTable
          :items="allResultItems"
          :showActions="true"
          @edit="$emit('edit-character', $event)"
          @delete="$emit('delete-character', $event)"
        />
      </div>

      <!-- 原始LLM回复（如果没有解析到结构化数据） -->
      <div
        v-if="showRawOutput"
        class="raw-text-output"
      >
        <p>LLM原始回复 (未能解析为结构化数据):</p>
        <pre>{{ rawOutput }}</pre>
      </div>
    </div>

    <div
      v-else-if="!isLoading && !errorMsg"
      class="no-results"
    >
      <p>点击"开始分析"以生成结果。源文本可通过头部按钮查看。</p>
    </div>

    <p
      v-if="errorMsg"
      class="error-message analysis-error panel-error-message"
    >
      分析错误: {{ errorMsg }}
    </p>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import CharacterTable from './CharacterTable.vue';

// eslint-disable-next-line no-unused-vars
const props = defineProps({
  analyzedItems: {
    type: Object,
    required: true,
    default: () => ({
      eraBackground: '',
      characters: [],
      specialItems: [],
      techniques: [],
      scenes: []
    })
  },
  rawOutput: {
    type: String,
    default: ''
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  errorMsg: {
    type: String,
    default: ''
  },
  canAddToChapter: {
    type: Boolean,
    default: false
  }
});

// 只显示角色数据用于表格显示
const allResultItems = computed(() => {
  const characters = (props.analyzedItems.characters || []).map(item => ({
    ...item,
    type: 'character'
  }));

  return characters;
});

// 如果没有解析到角色数据但有原始输出，则显示原始输出
const showRawOutput = computed(() => {
  return props.rawOutput &&
    (!props.analyzedItems.characters || props.analyzedItems.characters.length === 0) &&
    !props.analyzedItems.eraBackground;
});

</script>

<style scoped>
.current-analysis-results-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.results-section {
  margin-top: 0;
  padding: 15px;
  background-color: #232136;
  border: 1px solid #44415a;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  flex-grow: 1;
  overflow-y: auto;
}

.era-background-text {
  padding: 8px 10px;
  background-color: #1e1c2f;
  border: 1px solid #44415a;
  border-radius: 4px;
  margin-bottom: 15px;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: #e0def4;
  font-size: 0.95em;
}

.era-background-text strong {
  color: #3e8fb0;
  margin-right: 5px;
}

/* 表格样式 */
.results-table-container {
  margin-top: 10px;
  overflow-x: auto;
}

.table-title {
  margin-top: 0;
  margin-bottom: 15px;
  color: #e0def4;
  font-size: 1.15em;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 1px solid #44415a;
}

.preset-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  background-color: #2a273f;
  color: #e0def4;
  table-layout: auto;
}

.preset-table th,
.preset-table td {
  border: 1px solid #44415a;
  padding: 10px 12px;
  text-align: left;
  vertical-align: middle;
}

.preset-table th {
  background-color: #232136;
  font-weight: 600;
  font-size: 0.9em;
  color: #b8b5d6;
}

.column-type {
  min-width: 60px;
  white-space: nowrap;
}
.column-name {
  min-width: 90px;
  white-space: nowrap;
}
.column-description {
  white-space: normal;
}
.column-actions {
  min-width: 60px;
  text-align: center;
  white-space: nowrap;
}

.cell-type,
.cell-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cell-description {
  color: #b8b5d6;
  line-height: 1.6;
  white-space: pre-line;
  word-break: break-word;
  font-size: 0.98em;
  max-width: 100%;
  overflow: hidden;
}

.desc-sep {
  color: #908caa;
  margin: 0 8px;
  font-weight: bold;
}

.cell-actions {
  text-align: right;
}

.edit-preset-button,
.delete-preset-button {
  background: none;
  border: none;
  padding: 0 4px;
  margin: 0 2px;
  cursor: pointer;
  color: #3e8fb0;
  transition: color 0.18s, background 0.18s;
  border-radius: 4px;
  font-size: 1em;
}
.edit-preset-button:hover,
.delete-preset-button:hover {
  color: #4ea8c6;
  background: #232f3e;
}

.raw-text-output {
  margin-top: 15px;
}

.raw-text-output p {
  font-size: 0.9em;
  color: #908caa;
  margin-bottom: 5px;
}

.raw-text-output pre {
  background-color: #1e1c2f;
  border: 1px solid #44415a;
  padding: 12px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
  font-size: 0.85em;
  color: #908caa;
  margin-top: 10px;
}

.no-results {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-results p {
  color: #908caa;
  font-style: italic;
  text-align: center;
  padding: 25px;
  background-color: #232136;
  border: 1px dashed #44415a;
  border-radius: 6px;
  font-size: 0.95em;
}

.panel-error-message {
  color: #ff6b6b;
  background-color: #4f2b2b;
  border: 1px solid #ff6b6b;
  padding: 10px 15px;
  border-radius: 4px;
  margin: 10px;
  font-size: 0.9em;
  text-align: center;
}

.no-data-cell {
  text-align: center;
  color: #908caa;
  font-style: italic;
  background: #232136;
}

.name-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: normal;
}
</style>